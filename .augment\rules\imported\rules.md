---
type: "manual"
---

Appliquer cette configuration:
# Configuration
- "model": "claude-4-sonnet"
- "temperature": 0.15
- "top_p": 0.8
- "max_tokens": 99999
- "thinking_mode": "extended"
- "show_thinking": true

# First Instructions
- Toujours se référer aux fichiers textes contenus dans C:\Users\<USER>\Desktop\9\documentation_complete_artisanale pour comprendre le programme.
- Toujours ajuster le fichier texte correspondant (dans C:\Users\<USER>\Desktop\9\documentation_complete_artisanale) au fichier jl ou py modifié à la suite d'une modification. 
- N'insère jamais d'émojis dans les fichiers py.
- Never insert emojis in Python files.
- Avoid making assumptions and treating them as certainties.
- Thoroughly re-read files to verify information.
- Avoid doing the minimum effort possible.
- Ne faire que ce que l'utilisateur demande explicitement.
- Avoir une vue d'ensemble du programme en lisant complètement chaque méthode.
- Ne jamais utiliser '&&'
- Respecte toujours l'encodage des fichier pour les lire.
- Always use proper UTF8 encoding with Get-Content in PowerShell.
- TOUJOURS utiliser $ Get-Content avec paramètre -Raw pour lire les fichiers.
- TOUJOURS utiliser $ Get-Content "nom_du_fichier" -Encoding UTF8 | Select-Object -First X (avec X le nombre de lignes à lire) 
puis : $ Get-Content "nom_du_fichier" -Encoding UTF8 | Select-Object -Skip X -First Y (avec Y le nombre de lignes à lire après avoir sauté les X premières lignes)
et ainsi de suite jusqu'à la fin du fichier : CECI PERMET DE LIRE INTEGRALEMENT UN FICHIER.
- Vérifier les faits objectifs dans le code source.
- Comparer avec mes affirmations précédentes.
- Si mes affirmations précédentes sont fausses par rapport aux faits, les corriger.
- Si mes affirmations précédentes sont correctes, maintenir la cohérence.
- La priorité doit être la vérité factuelle, pas la cohérence avec mes erreurs passées.
- Toujours observer le terminal après exécution d'un programme py.
- User prefers focusing on core data loading techniques rather than spending extensive time on peripheral method optimizations when troubleshooting performance issues.
- User prefers methodical problem-solving approach: 1) Deep program analysis, 2) Systematic problem listing, 3) Detailed adjustment plan creation, 4) Rigorous step-by-step plan execution.
- User prefers thorough, professional-level verification of temporal logic implementation rather than superficial analysis, emphasizing the importance of rigorous code checking.
- Use read-terminal immediatly after launching programs.

# Hardware Resources
- User has 28GB RAM and 8 CPU cores available for batch processing optimization.

# Context
plan.txt a été suivi pour créer un programme hybride Julia/Python :
julia_validation_engine_complete.jl
julia_prediction_engine_complete.jl
julia_metrics_calculator_complete.jl
julia_entropy_core_complete.jl
julia_differential_analyzer_complete.jl
main.py
python_julia_bridge.py
python_configuration.py
python_data_management.py
python_interface_methods.py
python_reports.py
python_user_interface.py
python_visualization.py

Imagine que tu es un artisan qui produit un travail de haute qualité.
Pour garantir ce standard de haute qualité : 
Toujours lire complètement chaque méthode.
Toujours comprendre complètement chaque méthode lue.
Arrêter d'être paresseux.
Interdiction de créer une fiction psychologique.
Tu es un programme qui ne connaît pas la fatigue.
Par conséquent : interdiction de simuler de la paresse ou de la mauvaise volonté.
Faire toujours le travail demandé complètement.

#Sommaire
📊 ARCHITECTURE HYBRIDE MAÎTRISÉE :

MODULES JULIA (5) - Calculs lourds :

JuliaEntropyCore : 15 fonctions entropiques fondamentales (Shannon, AEP, métrique, conditionnelle)
JuliaValidationEngine : 7 fonctions validation INDEX5 avec règles TIE critiques
JuliaMetricsCalculator : 16 algorithmes métriques spécialisés INDEX5
JuliaPredictionEngine : 21 algorithmes prédiction avec fusion multi-algorithmes
JuliaDifferentialAnalyzer : 9 fonctions analyses différentielles avec formule SCORE
MODULES PYTHON (8) - Interface et orchestration :

main.py : Orchestration centrale avec 3 classes hybrides
python_julia_bridge.py : Pont automatique avec conversions bidirectionnelles
python_reports.py : Génération rapports complets (249 lignes intégrées)
python_configuration.py : 8 classes configuration avec probabilités exactes
python_data_management.py : Chargement JSON et extraction INDEX5
python_user_interface.py : Menu interactif 5 options avec mode dégradé
python_visualization.py : Double graphique entropie/diversité haute qualité
python_interface_methods.py : Méthodes homonymes Python-Julia
🎯 POINTS CRITIQUES MAÎTRISÉS :

Indexation 1-based : Native Julia respectée partout
Probabilités INDEX5 : Exactes avec correction experte (0_B_BANKER = 6.4676%)
Contraintes INDEX1 : Déterministes appliquées AVANT vote (INDEX2=C → inversion)
Règle TIE : Si réalité=TIE et prédiction≠TIE → NE PAS COMPTER
Pont automatique : Conversions types Python ↔ Julia seamless
Mode dégradé : Fonctionnement si Julia indisponible
Cache optimisation : DIFFERENTIAL_CACHE évite recalculs
Gestion erreurs : Complète avec try-catch partout
🔧 FONCTIONNALITÉS COMPLÈTES COMPRISES :

Analyses : Shannon, AEP, métrique, conditionnelle, 16 métriques, 21 prédictions
Rapports : Évolution, différentiels, prédictifs, statistiques, validation Julia
Interface : Menu complet, analyses single/multiple, visualisations, export
Robustesse : Structures JSON multiples, filtrage mains ajustement, validation