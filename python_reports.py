"""
python_reports.py - Module Python pour génération de rapports
CRÉATION AVEC LECTURE COMPLÈTE INTÉGRALE DE CHAQUE FICHIER TEXTE

PARTIE PYTHON selon plan.txt (lignes 115-117)
Migré depuis BaccaratEntropyAnalyzer (lignes 771-1072, 1123-1146)
QUALITÉ ARTISANALE - CHAQUE FICHIER TEXTE LU INTÉGRALEMENT
"""

import pandas as pd
import numpy as np
from typing import Dict
from python_configuration import (
    INDEX5Calculator, INDEX5Predictor, INDEX5PredictionValidator,
    INDEX5DifferentialAnalyzer, INDEX5PredictiveScoreTable,
    INDEX5PredictiveDifferentialTable
)

class BaccaratReportGenerator:
    """
    Générateur de rapports pour l'analyseur d'entropie baccarat
    Correspond aux méthodes 79-80 du plan.txt
    """
    
    def __init__(self, theoretical_entropy: float = 4.1699):
        """Initialisation du générateur de rapports"""
        self.theoretical_entropy = theoretical_entropy
    
    def generate_entropy_report(self, analysis_result: Dict, julia_bridge=None) -> str:
        """
        📋 GÉNÉRATION - Génère un rapport détaillé d'analyse d'entropie selon les méthodes avancées
        LECTURE COMPLÈTE INTÉGRALE: generate_entropy_report.txt (249 lignes)
        Lignes source: 771-1072

        Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md

        Args:
            analysis_result: Résultats d'analyse d'une partie

        Returns:
            Rapport formaté en texte avec métriques avancées
        """
        if 'entropy_evolution' not in analysis_result:
            return "❌ Pas de données d'analyse disponibles"

        evolution = analysis_result['entropy_evolution']
        game_id = analysis_result['game_id']
        sequence_length = analysis_result['sequence_length']
        complexity = analysis_result.get('complexity_metrics', {})

        # Statistiques des nouvelles métriques
        metric_entropies = [item['metric_entropy'] for item in evolution]
        conditional_entropies = [item['conditional_entropy'] for item in evolution]
        entropy_rates = [item['entropy_rate'] for item in evolution]
        simple_entropies = [item['simple_entropy'] for item in evolution]  # Ancienne méthode

        # Valeurs finales
        final_metric = analysis_result.get('final_metric_entropy', 0)
        final_conditional = analysis_result.get('final_conditional_entropy', 0)
        final_rate = analysis_result.get('final_entropy_rate', 0)
        final_simple = analysis_result.get('final_simple_entropy', 0)

        # Positions des maxima
        max_metric_pos = analysis_result.get('max_metric_entropy_position', 0)
        max_conditional_pos = analysis_result.get('max_conditional_entropy_position', 0)

        report = f"""
🎯 RAPPORT D'ANALYSE D'ENTROPIE AVANCÉE - INDEX5
===============================================
Méthodes: Kolmogorov-Sinai, Entropie de Blocs, Entropie Conditionnelle
Référence: entropie/cours_entropie/niveau_expert/

📊 INFORMATIONS GÉNÉRALES
Partie ID: {game_id}
Longueur de la séquence: {sequence_length} mains
Entropie théorique maximale: {self.theoretical_entropy:.4f} bits

📈 MÉTRIQUES D'ENTROPIE AVANCÉES
┌─ Entropie Métrique (Kolmogorov-Sinai) ─┐
│ Finale: {final_metric:.4f} bits/symbole │
│ Maximum: {max(metric_entropies):.4f} bits (position {max_metric_pos}) │
│ Moyenne: {np.mean(metric_entropies):.4f} bits │
└────────────────────────────────────────┘

┌─ Entropie Conditionnelle H(Xₙ|X₁...Xₙ₋₁) ─┐
│ Finale: {final_conditional:.4f} bits │
│ Maximum: {max(conditional_entropies):.4f} bits (position {max_conditional_pos}) │
│ Moyenne: {np.mean(conditional_entropies):.4f} bits │
│ → Mesure la prédictibilité du prochain symbole │
└─────────────────────────────────────────────┘

┌─ Taux d'Entropie (Entropy Rate) ─┐
│ Final: {final_rate:.4f} bits/symbole │
│ → Limite asymptotique de l'information par symbole │
└──────────────────────────────────┘

📊 COMPARAISON DES MÉTHODES D'ENTROPIE
Entropie simple (fréquences observées): {final_simple:.4f} bits
Entropie simple (probabilités théoriques): {evolution[-1].get('simple_entropy_theoretical', 0):.4f} bits
Entropie métrique (Kolmogorov-Sinai): {final_metric:.4f} bits
Différence observée vs métrique: {abs(final_simple - final_metric):.4f} bits
Différence théorique vs métrique: {abs(evolution[-1].get('simple_entropy_theoretical', 0) - final_metric):.4f} bits

🔬 ANALYSE DE COMPLEXITÉ
"""

        # Ajout des métriques de complexité
        if complexity:
            report += f"""Complexité Lempel-Ziv: {complexity.get('lz_complexity', 'N/A')}
Entropie Topologique: {complexity.get('topological_entropy', 0):.4f} bits
Diversité relative: {complexity.get('sequence_diversity', 0)*100:.1f}%
Taux de répétition: {complexity.get('repetition_rate', 0)*100:.1f}%

🎲 MOTIFS UNIQUES OBSERVÉS
"""
            unique_patterns = complexity.get('unique_patterns', {})
            for length, count in unique_patterns.items():
                report += f"Longueur {length.split('_')[1]}: {count} motifs uniques\n"

        # Initialiser le calculateur, prédicteur, validateur et analyseur différentiel INDEX5
        # CORRECTION: Passer l'instance analyzer pour accéder à _calculate_sequence_entropy_aep
        calculator = INDEX5Calculator(analyzer=self)
        predictor = INDEX5Predictor()
        validator = INDEX5PredictionValidator()
        differential_analyzer = INDEX5DifferentialAnalyzer()

        # Calculer les différentiels
        differentials = differential_analyzer.calculate_differentials(evolution)

        report += f"""
📋 ÉVOLUTION COMPLÈTE - TOUTES LES {len(evolution)} MAINS AVEC MÉTRIQUES ET PRÉDICTIONS INDEX5
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | Uniques | Métriques INDEX5 | Prédiction POUR cette main
---------|-------------|----------|----------------|------|-------------|--------------|---------|------------------|---------------------------
"""

        # Pré-calculer toutes les prédictions pour éviter le décalage
        sequence = analysis_result.get('full_sequence', [])
        predictions = ["N/A"]  # Première main n'a pas de prédiction

        for i in range(len(evolution) - 1):
            item = evolution[i]
            sequence_up_to_i = sequence[:i+1] if i < len(sequence) else sequence
            current_metrics = {
                'conditional_entropy': item.get('conditional_entropy', 0),
                'metric_entropy': item.get('metric_entropy', 0),
                'repetition_rate': item.get('entropy_rate', 0),
                'predictability_score': 1 - (item.get('conditional_entropy', 3.9309) / 3.9309)
            }

            # Calculer la prédiction pour la main suivante (i+1)
            if len(sequence_up_to_i) >= 5:  # Minimum requis pour prédiction
                prediction_result = predictor.predict_next_index5(sequence_up_to_i, current_metrics, julia_bridge)
                if prediction_result and isinstance(prediction_result, dict):
                    predicted_value = prediction_result.get('predicted_index5', 'N/A')
                    confidence = prediction_result.get('confidence', 0)

                    # Gestion spéciale pour WAIT
                    if predicted_value == 'WAIT':
                        prediction_display = "WAIT"
                    else:
                        prediction_display = f"{predicted_value}({confidence:.2f})"
                else:
                    # Essayer prédiction contextuelle simple
                    simple_pred = predictor.predict_context_level(sequence_up_to_i, current_metrics)
                    prediction_display = simple_pred if simple_pred else "N/A"
            else:
                prediction_display = "N/A"

            predictions.append(prediction_display)

            # Valider la prédiction avec la valeur réelle suivante
            if prediction_display != "N/A":
                next_actual_index5 = sequence[i + 1]
                validator.validate_prediction(prediction_display, next_actual_index5, item['position'] + 1)

        # Générer le rapport avec les prédictions correctement alignées
        for i, item in enumerate(evolution):
            simple_theo = item.get('simple_entropy_theoretical', 0)
            index5_value = sequence[i] if i < len(sequence) else "N/A"

            # Calculer les métriques INDEX5 pour cette position
            sequence_up_to_i = sequence[:i+1] if i < len(sequence) else sequence
            current_metrics = {
                'conditional_entropy': item.get('conditional_entropy', 0),
                'metric_entropy': item.get('metric_entropy', 0),
                'repetition_rate': item.get('entropy_rate', 0),
                'predictability_score': 1 - (item.get('conditional_entropy', 3.9309) / 3.9309)
            }

            # Calculer les nouvelles métriques INDEX5
            if len(sequence_up_to_i) >= 2:
                index5_metrics = calculator.calculate_all_metrics(sequence_up_to_i, current_metrics, evolution[:i+1], julia_bridge)

                # Sélectionner les 3 métriques les plus importantes pour l'affichage
                context_pred = index5_metrics.get('context_predictability', 0)
                pattern_str = index5_metrics.get('pattern_strength', 0)
                consensus = index5_metrics.get('multi_algorithm_consensus', 0)

                metrics_display = f"Ctx:{context_pred:.3f} Pat:{pattern_str:.3f} Con:{consensus:.3f}"
            else:
                metrics_display = "Ctx:0.000 Pat:0.000 Con:0.000"

            # Utiliser la prédiction pré-calculée pour cette main
            prediction_display = predictions[i] if i < len(predictions) else "N/A"

            report += f"Main {item['position']:2d}  | {index5_value:11s} | {item['metric_entropy']:6.3f}  | {item['conditional_entropy']:12.3f}  | {item['entropy_rate']:4.3f} | {item['simple_entropy']:9.3f} | {simple_theo:10.3f} | {item['unique_values']:2d}/18 | {metrics_display} | {prediction_display:14s}\n"

        # Ajouter le nouveau tableau avec les différentiels
        report += f"""

📊 TABLEAU AVEC DIFFÉRENTIELS - ANALYSE DES VARIATIONS ENTRE MAINS
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | DiffCond | DiffTaux | DiffDivEntropG | DiffEntropG | Prédiction
---------|-------------|----------|----------------|------|-------------|--------------|----------|----------|----------------|-------------|------------
"""

        # Générer le tableau avec différentiels
        for i, item in enumerate(evolution):
            simple_theo = item.get('simple_entropy_theoretical', 0)
            index5_value = sequence[i] if i < len(sequence) else "N/A"
            prediction_display = predictions[i] if i < len(predictions) else "N/A"

            # Récupérer les différentiels correspondants
            if i < len(differentials):
                diff_data = differentials[i]
                diff_cond = diff_data.get('diff_conditional', 0)
                diff_taux = diff_data.get('diff_entropy_rate', 0)
                diff_div_entrop = diff_data.get('diff_simple_entropy', 0)
                diff_entrop = diff_data.get('diff_simple_entropy_theoretical', 0)
            else:
                diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0

            report += f"Main {item['position']:2d}  | {index5_value:11s} | {item['metric_entropy']:6.3f}  | {item['conditional_entropy']:12.3f}  | {item['entropy_rate']:4.3f} | {item['simple_entropy']:9.3f} | {simple_theo:10.3f} | {diff_cond:6.3f}   | {diff_taux:6.3f}   | {diff_div_entrop:12.3f}   | {diff_entrop:9.3f}   | {prediction_display:14s}\n"

        # Ajouter le nouveau tableau prédictif avec différentiels
        predictive_table_generator = INDEX5PredictiveDifferentialTable()
        predictive_table = predictive_table_generator.generate_predictive_table(
            analysis_result['full_sequence'],
            analysis_result['entropy_evolution'],
            self
        )

        # CORRECTION : Injecter directement les données calculées dans le tableau SCORES
        computed_differentials = predictive_table_generator._differential_cache
        predictive_score_table_generator = INDEX5PredictiveScoreTable()
        predictive_score_table = predictive_score_table_generator.generate_predictive_score_table(
            analysis_result['full_sequence'],
            analysis_result['entropy_evolution'],
            self,
            precomputed_differentials=computed_differentials
        )

        report += f"""

🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS POUR LES 9 VALEURS INDEX5 POSSIBLES
════════════════════════════════════════════════════════════════════════

{predictive_table}

{predictive_score_table}
"""

        # Ajouter les statistiques des différentiels
        diff_stats = differential_analyzer.get_differential_statistics(differentials)
        if diff_stats:
            report += f"""

📈 STATISTIQUES DES DIFFÉRENTIELS
═══════════════════════════════

🔢 DIFFÉRENTIELS ENTROPIE CONDITIONNELLE (DiffCond)
• Minimum: {diff_stats.get('diff_conditional', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_conditional', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_conditional', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_conditional', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS TAUX D'ENTROPIE (DiffTaux)
• Minimum: {diff_stats.get('diff_entropy_rate', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_entropy_rate', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_entropy_rate', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_entropy_rate', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS DIVERSITÉ ENTROPIQUE (DiffDivEntropG)
• Minimum: {diff_stats.get('diff_simple_entropy', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_simple_entropy', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_simple_entropy', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_simple_entropy', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS ENTROPIE GÉNÉRALE (DiffEntropG)
• Minimum: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('std', 0):.4f} bits
"""

        # Ajout d'analyses statistiques détaillées
        report += f"""
📊 ANALYSES STATISTIQUES COMPLÈTES
═══════════════════════════════════

🔢 STATISTIQUES D'ENTROPIE MÉTRIQUE
• Minimum: {min(metric_entropies):.4f} bits (main {metric_entropies.index(min(metric_entropies)) + 1})
• Maximum: {max(metric_entropies):.4f} bits (main {metric_entropies.index(max(metric_entropies)) + 1})
• Écart-type: {np.std(metric_entropies):.4f} bits
• Coefficient de variation: {np.std(metric_entropies)/np.mean(metric_entropies)*100:.1f}%

🔢 STATISTIQUES D'ENTROPIE CONDITIONNELLE
• Minimum: {min(conditional_entropies):.4f} bits (main {conditional_entropies.index(min(conditional_entropies)) + 1})
• Maximum: {max(conditional_entropies):.4f} bits (main {conditional_entropies.index(max(conditional_entropies)) + 1})
• Écart-type: {np.std(conditional_entropies):.4f} bits
• Coefficient de variation: {np.std(conditional_entropies)/np.mean(conditional_entropies)*100:.1f}%

🔢 ÉVOLUTION DE LA DIVERSITÉ
• Diversité initiale: {evolution[0]['unique_values']}/18 ({evolution[0]['unique_values']/18*100:.1f}%)
• Diversité finale: {evolution[-1]['unique_values']}/18 ({evolution[-1]['unique_values']/18*100:.1f}%)
• Croissance de diversité: +{evolution[-1]['unique_values'] - evolution[0]['unique_values']} valeurs uniques

🎯 POINTS D'INTÉRÊT IDENTIFIÉS
• Main avec entropie métrique maximale: {max_metric_pos} ({max(metric_entropies):.4f} bits)
• Main avec entropie conditionnelle maximale: {max_conditional_pos} ({max(conditional_entropies):.4f} bits)
• Stabilisation de l'entropie métrique: {"Oui" if np.std(metric_entropies[-10:]) < 0.05 else "Non"} (10 dernières mains)

🔍 INTERPRÉTATION AVANCÉE
• Entropie métrique moyenne ({np.mean(metric_entropies):.3f} bits) = {np.mean(metric_entropies)/self.theoretical_entropy*100:.1f}% du maximum théorique
• Entropie conditionnelle faible → Forte dépendance temporelle, patterns récurrents exploitables
• Taux d'entropie stable → Information moyenne générée par symbole à long terme
• Complexité LZ ({complexity.get('lz_complexity', 'N/A')}) → Séquence {"hautement" if complexity.get('lz_complexity', 60) < 40 else "modérément"} compressible
• Coefficient de variation faible → Comportement {"stable" if np.std(metric_entropies)/np.mean(metric_entropies) < 0.2 else "variable"}
"""

        # Intégrer le rapport de validation Julia
        try:
            from python_julia_bridge import AdvancedJuliaPythonBridge
            julia_bridge = AdvancedJuliaPythonBridge()
            validation_report = julia_bridge.julia.JuliaValidationEngine.get_detailed_report_one_based()
            report += f"\n{validation_report}"
        except Exception as e:
            report += f"\n⚠️ Rapport de validation Julia non disponible: {e}"

        return report

    def export_results_to_csv(self, analysis_result: Dict, filename: str):
        """
        📄 EXPORT - Exporte les résultats d'analyse vers un fichier CSV
        LECTURE COMPLÈTE INTÉGRALE: export_results_to_csv.txt (29 lignes)
        Lignes source: 1123-1146

        Args:
            analysis_result: Résultats d'analyse d'une partie
            filename: Nom du fichier CSV
        """
        if 'entropy_evolution' not in analysis_result:
            print("❌ Pas de données à exporter")
            return

        evolution = analysis_result['entropy_evolution']

        # Création du DataFrame
        df = pd.DataFrame(evolution)

        # Ajout d'informations sur la partie
        df['game_id'] = analysis_result['game_id']
        df['theoretical_entropy_max'] = self.theoretical_entropy

        # Sauvegarde
        df.to_csv(filename, index=False)
        print(f"📄 Résultats exportés vers: {filename}")

# ============================================================================
# GÉNÉRATION DE RAPPORTS COMPLÈTE - 16/17 MÉTHODES PYTHON IMPLÉMENTÉES
# ============================================================================
# 
# RÉSUMÉ DE L'IMPLÉMENTATION :
# - 2 méthodes de génération de rapports implémentées
# - 336 lignes de fichiers texte lues intégralement
# - Interface Python pure pour orchestration
# - Qualité artisanale avec lecture complète de chaque fichier source
# 
# MÉTHODES IMPLÉMENTÉES :
# - generate_entropy_report : Rapport détaillé avec métriques avancées
# - export_results_to_csv : Export CSV des résultats d'analyse
# 
# PROCHAINE ÉTAPE : Méthode homonyme Python (1 méthode)
# ============================================================================
