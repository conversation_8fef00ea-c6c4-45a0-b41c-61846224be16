DOCUMENTATION COMPLÈTE ARTISANALE - julia_entropy_core_complete.jl
═══════════════════════════════════════════════════════════════════════

📋 INFORMATIONS GÉNÉRALES
═══════════════════════════
• Fichier: julia_entropy_core_complete.jl
• Lignes totales: 618
• Type: Module Julia pour calculs entropiques fondamentaux
• Architecture: Hybride Julia-Python avec indexation 1-based
• Qualité: Artisanale - Chaque fichier texte lu intégralement

📊 STRUCTURE DU MODULE
═══════════════════════
Module: JuliaEntropyCore

🔧 EXPORTS (lignes 12-19):
- safe_log_one_based
- validate_probabilities_one_based  
- calculate_shannon_entropy_one_based
- calculate_sequence_entropy_aep_one_based
- calculate_conditional_entropy_one_based
- estimate_metric_entropy_one_based
- calculate_block_entropies_one_based
- calculate_block_entropies_raw_one_based
- calculate_block_entropy_evolution_one_based
- calculate_sequence_complexity_one_based
- approximate_lz_complexity_one_based
- approximate_topological_entropy_one_based
- calculate_repetition_rate_one_based
- calculate_simple_entropy_theoretical_one_based
- calculate_theoretical_entropy_one_based
- THEORETICAL_PROBS_ONE_BASED

🎯 CONSTANTES CRITIQUES (lignes 21-39):
THEORETICAL_PROBS_RAW_ONE_BASED - Probabilités théoriques INDEX5 EXACTES
• "0_A_BANKER" => 8.5136, "1_A_BANKER" => 8.6389
• "0_B_BANKER" => 6.4676, "1_B_BANKER" => 6.5479  # CORRIGÉ: était 7.6907
• "0_C_BANKER" => 7.7903, "1_C_BANKER" => 7.8929
• "0_A_PLAYER" => 8.5240, "1_A_PLAYER" => 8.6361
• "0_B_PLAYER" => 7.6907, "1_B_PLAYER" => 7.7888
• "0_C_PLAYER" => 5.9617, "1_C_PLAYER" => 6.0352
• "0_A_TIE" => 1.7719, "1_A_TIE" => 1.7978
• "0_B_TIE" => 1.6281, "1_B_TIE" => 1.6482
• "0_C_TIE" => 1.3241, "1_C_TIE" => 1.3423

THEORETICAL_PROBS_ONE_BASED - Version normalisée (total = 1.0)

📋 MÉTHODES PRINCIPALES ANALYSÉES
═══════════════════════════════════

1. safe_log_one_based(x) - Lignes 51-67
   • Sécurité: Calcul sécurisé du logarithme avec gestion de log(0)
   • Évite erreurs mathématiques en remplaçant valeurs nulles par epsilon
   • Retourne: log2(max(x, 1e-10))

2. validate_probabilities_one_based(probs) - Lignes 69-85
   • Validation: Vérification que les probabilités sont valides
   • Contrôles: somme ≈ 1.0, toutes valeurs ≥ 0
   • Retourne: true si valide, false sinon

3. calculate_shannon_entropy_one_based(probs) - Lignes 87-102
   • Calcul: Entropie de Shannon H = -Σ p(x) * log2(p(x))
   • Sécurité: Utilise safe_log_one_based
   • Retourne: Entropie en bits

4. calculate_sequence_entropy_aep_one_based(sequence) - Lignes 125-153
   • Calcul: Entropie AEP (Asymptotic Equipartition Property)
   • Source: Elements of Information Theory - ligne 1919
   • Formule: -(1/n) × ∑log₂(p_théo(xᵢ)) où p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)
   • Algorithme:
     - Pour chaque valeur: total_log_prob += log2(THEORETICAL_PROBS_ONE_BASED[value])
     - Gestion erreurs: epsilon = 1e-10 si probabilité = 0 ou valeur inconnue
     - Retour: -total_log_prob / length(sequence)
   • Indexation: 1-based native Julia
   • Retourne: Float64 entropie AEP de la séquence

5. calculate_conditional_entropy_one_based(sequence, block_size) - Lignes 132-180
   • Calcul: Entropie conditionnelle H(X_n|X_1...X_{n-1})
   • Méthode: Blocs de taille variable, transitions conditionnelles
   • Complexité: O(n * block_size)
   • Retourne: Entropie conditionnelle moyenne

6. estimate_metric_entropy_one_based(sequence, max_length) - Lignes 333-364
   • Calcul: Entropie métrique h_μ(T) selon Kolmogorov-Sinai
   • Formule: h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
   • Méthode: Calcul rigoureux de la limite H(n)/n
   • Retourne: Estimation de l'entropie métrique

7. calculate_block_entropy_evolution_one_based(sequence, max_block_length) - Lignes 205-258
   • Calcul: Évolution position par position de toutes les métriques
   • Métriques: Shannon observée/théorique, conditionnelle, métrique, taux
   • Structure: Vector{Dict{String, Any}} avec résultats détaillés
   • Indexation: 1-based native Julia
   • Retourne: Évolution complète pour analyse

8. calculate_block_entropies_one_based(sequence, max_length) - Lignes 419-457
   • Calcul: Entropie pour blocs de différentes longueurs
   • Méthode: AEP pour longueur 1, sous-séquences pour longueur > 1
   • Correction: Utilise probabilités théoriques pour blocs longueur 1
   • Retourne: Vector{Float64} des entropies par longueur

9. calculate_block_entropies_raw_one_based(sequence, max_length) - Lignes 375-407
   • Calcul: Entropie de blocs SANS normalisation
   • Usage: Pour calcul correct de l'entropie métrique
   • Méthode: AEP sur toutes les sous-séquences
   • Retourne: Vector{Float64} des entropies brutes

10. calculate_sequence_complexity_one_based(sequence) - Lignes 466-493
    • Calcul: Diverses métriques de complexité
    • Métriques: Motifs uniques, LZ, topologique, diversité, répétition
    • Retourne: Dict{String, Any} avec toutes les complexités

11. approximate_lz_complexity_one_based(sequence) - Lignes 502-533
    • Calcul: Approximation complexité de Lempel-Ziv
    • Méthode: Dictionnaire de motifs non vus
    • Algorithme: Recherche plus long préfixe non vu
    • Retourne: Int64 complexité LZ

12. approximate_topological_entropy_one_based(sequence) - Lignes 545-594
    • Calcul: Entropie topologique respectant principe variationnel
    • Formule: h_top = lim_{n→∞} (1/n) log(N(n))
    • Correction: Assure h_top ≥ h_μ (principe variationnel)
    • Retourne: Float64 entropie topologique

13. calculate_repetition_rate_one_based(sequence) - Lignes 601-615
    • Calcul: Taux de répétition dans la séquence
    • Méthode: Compte répétitions immédiates
    • Formule: répétitions / (longueur - 1)
    • Retourne: Float64 taux de répétition

14. calculate_simple_entropy_theoretical_one_based(sequence) - Lignes 162-164
    • Calcul: Entropie théorique simple (wrapper AEP)
    • Source: _calculate_simple_entropy_theoretical.txt (11 lignes)
    • Lignes source: 455-460
    • Méthode: Délégation vers calculate_sequence_entropy_aep_one_based()
    • Compatibilité: Maintenu pour code existant
    • Retourne: Float64 entropie théorique

15. calculate_theoretical_entropy_one_based() - Lignes 176-181
    • Calcul: Entropie théorique maximale INDEX5
    • Méthode: Shannon sur probabilités théoriques normalisées
    • Données: THEORETICAL_PROBS_ONE_BASED
    • Valeur: Entropie théorique maximale du système INDEX5
    • Retourne: Float64 entropie théorique maximale

📊 ARCHITECTURE ET QUALITÉ
═══════════════════════════
• Indexation: 1-based native Julia (cohérent)
• Sécurité: Gestion log(0) avec safe_log_one_based
• Corrections expertes: AEP, principe variationnel, formules rigoureuses
• Références: Cours d'entropie niveau expert
• Qualité: Artisanale - Lecture intégrale de chaque fichier texte source

🎯 POINTS CRITIQUES
═══════════════════
• Probabilités théoriques INDEX5 exactes avec correction ligne 25
• Calculs AEP pour cohérence mathématique
• Entropie métrique selon Kolmogorov-Sinai rigoureux
• Principe variationnel respecté pour entropie topologique
• Indexation 1-based cohérente dans tout le module

TOTAL: 618 lignes - Module fondamental complet pour calculs entropiques
