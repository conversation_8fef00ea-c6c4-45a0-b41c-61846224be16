DOCUMENTATION COMPLÈTE ARTISANALE - main.py
═══════════════════════════════════════════════════

📋 INFORMATIONS GÉNÉRALES
═══════════════════════════
• Fichier: main.py
• Lignes totales: 191
• Type: Point d'entrée principal de l'architecture hybride Julia-Python
• Rôle: Orchestration centrale selon plan.txt (lignes 512-588)
• Architecture: Classes hybrides avec pont automatique Julia
• Qualité: Artisanale - Orchestration complète

📊 STRUCTURE DU FICHIER
═══════════════════════
🔧 IMPORTS (lignes 9-25):
- sys, os (système)
- typing (Dict, List, Any)
- python_data_management (BaccaratDataManager)
- python_user_interface (BaccaratUserInterface, main as user_main)
- python_visualization (BaccaratVisualization)
- python_configuration (toutes les classes INDEX5)
- python_reports (BaccaratReportGenerator)
- python_interface_methods (PythonInterfaceMethods)
- python_julia_bridge (AdvancedJuliaPythonBridge)

📋 CLASSES PRINCIPALES ANALYSÉES
═══════════════════════════════════

1. HybridBaccaratEntropyAnalyzer - <PERSON><PERSON><PERSON> 27-103
   • Rôle: Analyseur hybride principal selon plan.txt (lignes 512-543)
   • Remplace: BaccaratEntropyAnalyzer avec calculs Julia
   • Architecture: Interface Python + Calculs Julia

   MÉTHODES:
   a) __init__(base, epsilon) - Lignes 33-64
      • Initialisation: Analyseur hybride selon plan.txt ligne 517-528
      • Julia bridge: AdvancedJuliaPythonBridge()
      • Probabilités théoriques: Conservation complète (lignes 520-525)
      • Entropie théorique: Calcul via Julia (ligne 528)
      • Composants Python: data_manager, visualization, report_generator
      • Affichage: Entropie théorique calculée

   b) load_baccarat_data(filepath) - Lignes 66-70
      • Fonction: Gestion des données - reste en Python (ligne 531)
      • Délégation: data_manager.load_baccarat_data()
      • Retourne: List[Dict] des données

   c) extract_index5_sequence(game_data) - Lignes 72-76
      • Fonction: Extraction séquence - reste en Python (ligne 532)
      • Délégation: data_manager.extract_index5_sequence()
      • Retourne: List[str] séquence INDEX5

   d) analyze_single_game(game_data, game_id) - Lignes 78-85
      • Fonction: Orchestration - appelle Julia pour calculs (ligne 540-543)
      • Délégation: TOUS les calculs délégués à Julia avec indexation 1-based
      • Méthode: julia_bridge.analyze_complete_game_one_based()
      • Retourne: Dict résultats d'analyse

   e) generate_entropy_report(analysis_result) - Lignes 87-91
      • Fonction: Génération rapports - reste en Python (ligne 545)
      • Délégation: report_generator.generate_entropy_report()
      • Retourne: String rapport formaté

   f) export_results_to_csv(analysis_result, filename) - Lignes 93-97
      • Fonction: Export CSV - reste en Python
      • Délégation: report_generator.export_results_to_csv()
      • Retourne: Void (export fichier)

   g) plot_entropy_evolution(analysis_result, save_path) - Lignes 99-103
      • Fonction: Visualisation - reste en Python
      • Délégation: visualization.plot_entropy_evolution()
      • Retourne: Graphique ou sauvegarde

2. HybridINDEX5Calculator - Lignes 105-124
   • Rôle: Calculateur hybride INDEX5 selon plan.txt (lignes 564-574)
   • Remplace: INDEX5Calculator avec calculs Julia

   MÉTHODES:
   a) __init__(analyzer) - Lignes 111-116
      • Initialisation: Calculateur hybride (ligne 566-568)
      • Julia bridge: AdvancedJuliaPythonBridge()
      • Analyzer: Référence optionnelle

   b) calculate_all_metrics(sequence_history, current_metrics, entropy_evolution) - Lignes 118-124
      • Fonction: Délégation complète à Julia avec indexation 1-based (ligne 571-574)
      • Méthode: julia_bridge.calculate_all_metrics_one_based()
      • Retourne: Dict métriques calculées

3. HybridINDEX5Predictor - Lignes 126-144
   • Rôle: Prédicteur hybride INDEX5 selon plan.txt (lignes 579-588)
   • Remplace: INDEX5Predictor avec calculs Julia

   MÉTHODES:
   a) __init__() - Lignes 132-136
      • Initialisation: Prédicteur hybride (ligne 581-582)
      • Julia bridge: AdvancedJuliaPythonBridge()

   b) predict_next_index5(sequence_history, all_metrics) - Lignes 138-144
      • Fonction: Délégation complète à Julia avec indexation 1-based (ligne 585-588)
      • Méthode: julia_bridge.predict_next_index5_one_based()
      • Retourne: Dict prédiction

📋 FONCTION PRINCIPALE
═══════════════════════

main() - Lignes 146-168
• Rôle: Point d'entrée principal de l'architecture hybride
• Selon: plan.txt - Interface utilisateur avec calculs Julia
• Affichage: Bannière architecture hybride Julia-Python
• Initialisation: HybridBaccaratEntropyAnalyzer()
• Lancement: user_main() interface utilisateur
• Gestion erreurs: Try-catch avec messages explicites

🎯 PROBABILITÉS THÉORIQUES CRITIQUES (lignes 44-54)
═══════════════════════════════════════════════════
Conservation exacte des probabilités INDEX5:
• '0_A_BANKER': 0.085136, '1_A_BANKER': 0.086389
• '0_B_BANKER': 0.064676, '1_B_BANKER': 0.065479
• '0_C_BANKER': 0.077903, '1_C_BANKER': 0.078929
• '0_A_PLAYER': 0.085240, '1_A_PLAYER': 0.086361
• '0_B_PLAYER': 0.076907, '1_B_PLAYER': 0.077888
• '0_C_PLAYER': 0.059617, '1_C_PLAYER': 0.060352
• '0_A_TIE': 0.017719, '1_A_TIE': 0.017978
• '0_B_TIE': 0.016281, '1_B_TIE': 0.016482
• '0_C_TIE': 0.013241, '1_C_TIE': 0.013423

📊 ARCHITECTURE HYBRIDE
═══════════════════════
• Orchestration: Python pour interface et gestion
• Calculs: Julia pour performance et précision
• Pont: AdvancedJuliaPythonBridge automatique
• Indexation: 1-based native Julia respectée
• Délégation: Complète pour tous les calculs lourds

🔧 INTÉGRATION MODULES
═══════════════════════
• data_management: Gestion données et extraction
• user_interface: Interface utilisateur complète
• visualization: Graphiques et visualisations
• reports: Génération rapports détaillés
• julia_bridge: Pont automatique vers Julia

🎯 POINTS CRITIQUES
═══════════════════
• Point d'entrée unique: main() orchestration centrale
• Classes hybrides: Remplacement transparent avec Julia
• Gestion erreurs: Messages explicites pour debugging
• Architecture: Respecte plan.txt lignes 512-588
• Performance: Calculs lourds délégués à Julia

TOTAL: 191 lignes - Orchestration centrale complète architecture hybride
