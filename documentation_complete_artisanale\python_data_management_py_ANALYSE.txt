DOCUMENTATION COMPLÈTE ARTISANALE - python_data_management.py
═══════════════════════════════════════════════════════════════

📋 INFORMATIONS GÉNÉRALES
═══════════════════════════
• Fichier: python_data_management.py
• Lignes totales: 123
• Type: Module Python pour gestion des données
• Partie: Python selon plan.txt (lignes 93-95)
• Migré depuis: BaccaratEntropyAnalyzer (lignes 193-271)
• Qualité: Artisanale - Chaque fichier texte lu intégralement

📊 STRUCTURE DU MODULE
═══════════════════════
🔧 IMPORTS (lignes 10-11):
- json (lecture fichiers JSON)
- typing (List, Dict)

📋 CLASSE PRINCIPALE ANALYSÉE
═══════════════════════════════

BaccaratDataManager - Lignes 13-105
• Rôle: Gestionnaire des données de baccarat - Interface et orchestration
• Correspond: Méthodes 65-66 du plan.txt

MÉTHODES ANALYSÉES:

1. __init__() - <PERSON><PERSON><PERSON> 19-21
   • Fonction: Initialisation du gestionnaire de données
   • Configuration: Aucun paramètre spécifique
   • Implémentation: Pass (classe utilitaire)

2. load_baccarat_data(filepath) - Lignes 23-60
   • Fonction: Charge les données de baccarat depuis le fichier JSON
   • Source: load_baccarat_data.txt (41 lignes)
   • Lignes source: 193-228
   • Rôle: Gère différentes structures JSON possibles et valide les données
   
   STRUCTURES JSON SUPPORTÉES:
   a) Structure avec clé: {"parties_condensees": [...]}
      • Détection: isinstance(data, dict) and 'parties_condensees' in data
      • Extraction: data['parties_condensees']
      • Message: "✅ Données chargées: X parties trouvées"
   
   b) Structure liste directe: [partie1, partie2, ...]
      • Détection: isinstance(data, list)
      • Extraction: data directement
      • Message: "✅ Données chargées: X parties trouvées"
   
   c) Structure non reconnue:
      • Message: "❌ Structure JSON non reconnue. Clés disponibles: ..."
      • Retour: [] (liste vide)
   
   GESTION ERREURS:
   • FileNotFoundError: "❌ Erreur: Fichier X non trouvé"
   • json.JSONDecodeError: "❌ Erreur JSON: {e}"
   • Retour sécurisé: [] en cas d'erreur
   
   • Retourne: List[Dict] des parties de baccarat

3. extract_index5_sequence(game_data) - Lignes 62-105
   • Fonction: Extrait la séquence INDEX5 d'une partie
   • Source: extract_index5_sequence.txt (47 lignes)
   • Lignes source: 230-271
   • Rôle: Filtre les mains d'ajustement et extrait uniquement les valeurs INDEX5 valides
   
   STRUCTURES PARTIE SUPPORTÉES:
   a) Structure avec 'hands': {"hands": [...]}
      • Parcours: for hand in game_data['hands']
      • Filtrage: main_number is not None AND INDEX5 non vide
      • Extraction: hand['INDEX5']
   
   b) Structure avec 'mains_condensees': {"mains_condensees": [...]}
      • Parcours: for main in game_data['mains_condensees']
      • Filtrage: main_number is not None AND index5 non vide
      • Extraction: main['index5']
   
   c) Structure non reconnue:
      • Message: "❌ Structure de partie non reconnue. Clés disponibles: ..."
      • Retour: [] (liste vide)
   
   FILTRAGE MAINS D'AJUSTEMENT:
   • Condition 1: main_number is not None (exclut mains ajustement)
   • Condition 2: INDEX5/index5 présent dans dictionnaire
   • Condition 3: INDEX5/index5 non vide après strip()
   • Résultat: Seules les mains valides pour analyse entropique
   
   MESSAGES:
   • Succès: "🔍 Séquence extraite: X mains valides (mains d'ajustement exclues)"
   
   • Retourne: List[str] séquence des valeurs INDEX5

🎯 POINTS CRITIQUES TECHNIQUES
═══════════════════════════════
• Robustesse: Gestion multiple structures JSON et parties
• Filtrage: Exclusion automatique mains d'ajustement
• Validation: Vérification existence et contenu non vide
• Messages: Informatifs pour debugging et suivi
• Sécurité: Try-catch avec retours par défaut
• Flexibilité: Support 'INDEX5' et 'index5' (casse différente)

📊 ARCHITECTURE GESTION DONNÉES
═══════════════════════════════
• Chargement: JSON avec structures multiples supportées
• Extraction: Séquences INDEX5 avec filtrage intelligent
• Validation: Contrôles existence et contenu
• Messages: Feedback utilisateur détaillé
• Robustesse: Gestion erreurs complète
• Qualité: 88 lignes de fichiers texte lues intégralement

🔧 INTÉGRATION SYSTÈME
═══════════════════════
• Utilisé par: HybridBaccaratEntropyAnalyzer dans main.py
• Dépendances: json (standard Python)
• Interface: Méthodes load et extract pour workflow complet
• Performance: Lecture directe JSON sans overhead

🎯 STRUCTURES DONNÉES SUPPORTÉES
═══════════════════════════════
FICHIERS JSON:
• {"parties_condensees": [partie1, partie2, ...]}
• [partie1, partie2, ...] (liste directe)

PARTIES:
• {"hands": [main1, main2, ...]} avec 'INDEX5'
• {"mains_condensees": [main1, main2, ...]} avec 'index5'

MAINS VALIDES:
• main_number: not None (exclut ajustements)
• INDEX5/index5: présent et non vide
• Contenu: après strip() non vide

TOTAL: 123 lignes - Gestionnaire données complet avec support structures multiples
