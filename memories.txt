# Configuration
- "model": "claude-4-sonnet"
- "temperature": 0.15
- "top_p": 0.8
- "max_tokens": 99999
- "thinking_mode": "extended"
- "show_thinking": true

# First Instructions
- Ne faire que ce que l'utilisateur demande explicitement.
- Pour chaque modification dans le programme mettre à jour le fichier vuedensemble.txt.
- Avoir une vue d'ensemble du programme.
- Toujours se référer à vuedensemble.txt pour avoir une vue d'ensemble du programme.
- Vérifier les faits objectifs dans le code source.
- Comparer avec mes affirmations précédentes.
- Si mes affirmations précédentes sont fausses par rapport aux faits, les corriger.
- Si mes affirmations précédentes sont correctes, maintenir la cohérence.
- La priorité doit être la vérité factuelle, pas la cohérence avec mes erreurs passées.
- Toujours observer le terminal après exécution d'un programme py.
- N'insère jamais d'émojis dans les fichiers py.
- Never insert emojis in Python files.
- Avoid making assumptions and treating them as certainties.
- <PERSON>oughly re-read files to verify information.
- Avoid doing the minimum effort possible.
- User prefers focusing on core data loading techniques rather than spending extensive time on peripheral method optimizations when troubleshooting performance issues.
- User prefers methodical problem-solving approach: 1) Deep program analysis, 2) Systematic problem listing, 3) Detailed adjustment plan creation, 4) Rigorous step-by-step plan execution.
- User prefers thorough, professional-level verification of temporal logic implementation rather than superficial analysis, emphasizing the importance of rigorous code checking.
- Use read-terminal immediatly after launching programs.
- Ne jamais utiliser '&&'
- Respecte toujours l'encodage des fichier pour les lire.
- Always use proper UTF8 encoding with Get-Content in PowerShell.
- TOUJOURS utiliser $ Get-Content avec paramètre -Raw pour lire les fichiers.
- TOUJOURS utiliser $ Get-Content "nom_du_fichier" -Encoding UTF8 | Select-Object -First X (avec X le nombre de lignes à lire) 
puis : $ Get-Content "nom_du_fichier" -Encoding UTF8 | Select-Object -Skip X -First Y (avec Y le nombre de lignes à lire après avoir sauté les X premières lignes)
et ainsi de suite jusqu'à la fin du fichier : CECI PERMET DE LIRE INTEGRALEMENT UN FICHIER.

# Hardware Resources
- User has 28GB RAM and 8 CPU cores available for batch processing optimization.

# Context
plan.txt a été suivi pour créer un programme hybride Julia/Python :
julia_validation_engine_complete.jl
julia_prediction_engine_complete.jl
julia_metrics_calculator_complete.jl
julia_entropy_core_complete.jl
julia_differential_analyzer_complete.jl
main.py
python_julia_bridge.py
python_configuration.py
python_data_management.py
python_interface_methods.py
python_reports.py
python_user_interface.py
python_visualization.py

Le programme original peut être a peu près compris grâce au sommaire C:\Users\<USER>\Desktop\9\tt\recent\partie\vuedensemble.txt

# Origins
Dans chaque fichier texte du dossier C:\Users\<USER>\Desktop\9\entropie_baccarat_analyzer
se trouve la méthode python d'origine qui fait partie soit d'un fichier jl soit d'un fichier py
