"""
python_julia_bridge.py - Pont Python-Julia pour architecture hybride
Selon plan.txt Phase 2

Permet l'appel des fonctions Julia depuis Python avec conversion automatique
des types et indexation 0-based → 1-based
"""

import os
import sys

# CORRECTIF : Ajouter le répertoire bin de Julia au PATH AVANT tout import
julia_bin_path = r"C:\Users\<USER>\.julia\juliaup\julia-1.11.5+0.x64.w64.mingw32\bin"
current_path = os.environ.get('PATH', '')
if julia_bin_path not in current_path:
    os.environ['PATH'] = julia_bin_path + os.pathsep + current_path

import julia
import numpy as np
from typing import List, Dict, Any, Optional, Union

class AdvancedJuliaPythonBridge:
    """
    Pont avancé Python-Julia pour l'architecture hybride
    Gère la conversion automatique des types et l'indexation
    """
    
    def __init__(self):
        """
        Initialise le pont Python-Julia et charge tous les modules
        """
        print("Initialisation du pont Python-Julia...")

        # Initialiser Julia avec configuration propre pour éviter conflits
        try:
            # Configurer Julia pour éviter les conflits PyCall
            os.environ['JULIA_PYTHONCALL_EXE'] = sys.executable

            # Désactiver les warnings MainInclude si possible
            os.environ['JULIA_WARN_OVERWRITE'] = '0'

            # CORRECTIF : Ajouter le répertoire bin de Julia au PATH pour libnghttp2-14.dll
            julia_bin_path = r"C:\Users\<USER>\.julia\juliaup\julia-1.11.5+0.x64.w64.mingw32\bin"
            current_path = os.environ.get('PATH', '')
            if julia_bin_path not in current_path:
                os.environ['PATH'] = julia_bin_path + os.pathsep + current_path
                print(f"Ajout du PATH Julia: {julia_bin_path}")

            # Importer Julia avec configuration propre
            import julia

            # Importer Main directement (les warnings MainInclude sont bénins)
            from julia import Main
            self.Main = Main

            # Charger TOUS les 5 modules Julia selon plan.txt (lignes 725-729)
            current_dir = os.path.dirname(os.path.abspath(__file__))

            # Liste des 5 modules Julia à charger
            julia_modules = [
                ("julia_entropy_core_complete.jl", "JuliaEntropyCore"),
                ("julia_metrics_calculator_complete.jl", "JuliaMetricsCalculator"),
                ("julia_prediction_engine_complete.jl", "JuliaPredictionEngine"),
                ("julia_differential_analyzer_complete.jl", "JuliaDifferentialAnalyzer"),
                ("julia_validation_engine_complete.jl", "JuliaValidationEngine")
            ]

            # Charger chaque module
            for julia_file_name, module_name in julia_modules:
                julia_file = os.path.join(current_dir, julia_file_name)

                if os.path.exists(julia_file):
                    print(f"Chargement de {julia_file_name}...")
                    self.Main.include(julia_file)
                    self.Main.eval(f"using .{module_name}")
                    print(f"✅ Module {module_name} chargé avec succès")
                else:
                    raise FileNotFoundError(f"Fichier Julia non trouvé: {julia_file}")

            print("🎉 TOUS LES 5 MODULES JULIA CHARGÉS AVEC INDEXATION 1-BASED")

        except Exception as e:
            print(f"Erreur lors du chargement de Julia: {e}")
            raise
    
    def _convert_to_julia_vector_string(self, python_list: List[str]) -> str:
        """
        Convertit une liste Python en code Julia Vector{String}
        """
        items = ', '.join([f'"{item}"' for item in python_list])
        return f'[{items}]'

    def _convert_to_julia_vector_float(self, python_list: List[float]) -> Any:
        """
        Convertit une liste Python en Vector{Float64} Julia
        """
        # Utiliser vec() pour s'assurer d'avoir un vecteur colonne 1D
        items = ", ".join([str(item) for item in python_list])
        return self.Main.eval(f'vec([{items}])')
    
    def _convert_from_julia_dict(self, julia_dict: Any) -> Dict[str, Any]:
        """
        Convertit un Dict Julia en dictionnaire Python
        """
        if julia_dict is None:
            return {}

        # Conversion basique - à améliorer selon les besoins
        try:
            return dict(julia_dict)
        except:
            return {}

    def convert_to_julia_vector(self, python_list: List[str]) -> str:
        """
        Convertit une liste Python en Vector{String} Julia
        """
        return self._convert_to_julia_vector_string(python_list)

    def convert_to_julia_vector_dict(self, python_list: List[Dict[str, Any]]) -> str:
        """
        Convertit une liste de dictionnaires Python en Vector{Dict} Julia
        """
        # Conversion complexe - pour l'instant retourner une représentation basique
        return "Vector{Dict{String, Any}}()"

    def convert_to_julia_dict(self, python_dict: Dict[str, Any]) -> str:
        """
        Convertit un dictionnaire Python en Dict Julia
        """
        if not python_dict:
            return "Dict{String, Any}()"

        items = []
        for key, value in python_dict.items():
            if isinstance(value, str):
                items.append(f'"{key}" => "{value}"')
            elif isinstance(value, (int, float)):
                items.append(f'"{key}" => {value}')
            else:
                items.append(f'"{key}" => "{str(value)}"')

        return f'Dict{{{", ".join(items)}}}'

    def convert_to_python_dict(self, julia_result: Any) -> Dict[str, Any]:
        """
        Convertit un résultat Julia en dictionnaire Python
        """
        return self._convert_from_julia_dict(julia_result)

    def _convert_to_julia_dict(self, python_dict: Dict[str, Any]) -> str:
        """
        Convertit un dictionnaire Python en code Dict{String, Any} Julia
        """
        if not python_dict:
            return "Dict{String, Any}()"

        items = []
        for key, value in python_dict.items():
            if isinstance(value, str):
                items.append(f'"{key}" => "{value}"')
            elif isinstance(value, (int, float)):
                items.append(f'"{key}" => {value}')
            else:
                items.append(f'"{key}" => "{str(value)}"')

        return f'Dict{{String, Any}}({", ".join(items)})'

    def _convert_to_julia_array_dict(self, python_list: List[Dict]) -> str:
        """
        Convertit une liste de dictionnaires Python en Vector{Dict{String, Any}} Julia
        """
        if not python_list:
            return "Vector{Dict{String, Any}}()"

        julia_dicts = []
        for item in python_list:
            julia_dict = self._convert_to_julia_dict(item)
            julia_dicts.append(julia_dict)

        return f'Vector{{Dict{{String, Any}}}}([{", ".join(julia_dicts)}])'
    
    # ==================== MÉTHODES ENTROPIQUES FONDAMENTALES ====================
    
    def safe_log_one_based(self, x: List[float], epsilon: float = 1e-10, base: float = 2.0) -> List[float]:
        """
        Appel Julia: safe_log_one_based()
        """
        julia_x = self._convert_to_julia_vector_float(x)
        result = self.Main.eval(f"JuliaEntropyCore.safe_log_one_based({julia_x}, epsilon={epsilon}, base={base})")
        return list(result)

    def validate_probabilities_one_based(self, probabilities: List[float]) -> List[float]:
        """
        Appel Julia: validate_probabilities_one_based()
        """
        julia_probs = self._convert_to_julia_vector_float(probabilities)
        result = self.Main.eval(f"JuliaEntropyCore.validate_probabilities_one_based({julia_probs})")
        return list(result)

    def calculate_shannon_entropy_one_based(self, probabilities: List[float]) -> float:
        """
        Appel Julia: calculate_shannon_entropy_one_based()
        """
        # Générer le code Julia directement
        items = ", ".join([str(item) for item in probabilities])
        julia_code = f'vec([{items}])'
        result = self.Main.eval(f"JuliaEntropyCore.calculate_shannon_entropy_one_based({julia_code})")
        return float(result)
    
    def calculate_sequence_entropy_aep_one_based(self, sequence: List[str]) -> float:
        """
        Appel Julia: calculate_sequence_entropy_aep_one_based()
        FORMULE MAÎTRE AEP
        """
        julia_code = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.calculate_sequence_entropy_aep_one_based({julia_code})")
        return float(result)

    def calculate_conditional_entropy_one_based(self, sequence: List[str]) -> float:
        """
        Appel Julia: calculate_conditional_entropy_one_based()
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.calculate_conditional_entropy_one_based({julia_sequence})")
        return float(result)

    def estimate_metric_entropy_one_based(self, sequence: List[str], max_length: int) -> float:
        """
        Appel Julia: estimate_metric_entropy_one_based()
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.estimate_metric_entropy_one_based({julia_sequence}, {max_length})")
        return float(result)

    def calculate_block_entropies_one_based(self, sequence: List[str], max_length: int) -> List[float]:
        """
        Appel Julia: calculate_block_entropies_one_based()
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.calculate_block_entropies_one_based({julia_sequence}, {max_length})")
        return list(result)
    
    def calculate_block_entropy_evolution_one_based(self, sequence: List[str], max_block_length: int = 5) -> List[Dict[str, Any]]:
        """
        Appel Julia: calculate_block_entropy_evolution_one_based()
        Méthode centrale d'analyse position par position
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.calculate_block_entropy_evolution_one_based({julia_sequence}, {max_block_length})")

        # Conversion du résultat Julia en liste de dictionnaires Python
        python_result = []
        for item in result:
            python_dict = self._convert_from_julia_dict(item)
            python_result.append(python_dict)

        return python_result

    def calculate_sequence_complexity_one_based(self, sequence: List[str]) -> Dict[str, float]:
        """
        Appel Julia: calculate_sequence_complexity_one_based()
        """
        julia_sequence = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.calculate_sequence_complexity_one_based({julia_sequence})")
        return self._convert_from_julia_dict(result)

    def calculate_block_entropies_raw_one_based(self, sequence: List[str], max_length: int) -> List[float]:
        """
        Appel Julia: calculate_block_entropies_raw_one_based()
        """
        julia_code = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.calculate_block_entropies_raw_one_based({julia_code}, {max_length})")
        return list(result)

    def approximate_lz_complexity_one_based(self, sequence: List[str]) -> float:
        """
        Appel Julia: approximate_lz_complexity_one_based()
        """
        julia_code = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.approximate_lz_complexity_one_based({julia_code})")
        return float(result)

    def approximate_topological_entropy_one_based(self, sequence: List[str]) -> float:
        """
        Appel Julia: approximate_topological_entropy_one_based()
        """
        julia_code = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.approximate_topological_entropy_one_based({julia_code})")
        return float(result)

    def calculate_repetition_rate_one_based(self, sequence: List[str]) -> float:
        """
        Appel Julia: calculate_repetition_rate_one_based()
        """
        julia_code = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.calculate_repetition_rate_one_based({julia_code})")
        return float(result)

    def calculate_simple_entropy_theoretical_one_based(self, sequence: List[str]) -> float:
        """
        Appel Julia: calculate_simple_entropy_theoretical_one_based()
        """
        julia_code = self._convert_to_julia_vector_string(sequence)
        result = self.Main.eval(f"JuliaEntropyCore.calculate_simple_entropy_theoretical_one_based({julia_code})")
        return float(result)
    
    # ==================== MÉTHODE D'ANALYSE COMPLÈTE ====================
    
    def analyze_complete_game_one_based(self, sequence: List[str]) -> Dict[str, Any]:
        """
        Analyse complète d'une partie avec TOUS les calculs en Julia
        Correspond à analyze_single_game() ligne 550-598 du plan
        """
        if not sequence:
            return {'error': 'Aucune séquence INDEX5 trouvée'}
        
        # Calcul évolution entropique (méthode centrale)
        entropy_evolution = self.calculate_block_entropy_evolution_one_based(sequence, max_block_length=4)
        
        if not entropy_evolution:
            return {'error': 'Impossible de calculer l\'évolution d\'entropie'}
        
        # Extraction des métriques finales
        final_analysis = entropy_evolution[-1]
        
        # Calcul de la complexité de la séquence
        complexity_metrics = self.calculate_sequence_complexity_one_based(sequence)
        
        return {
            'sequence_length': len(sequence),
            'full_sequence': sequence,
            'entropy_evolution': entropy_evolution,
            
            # Métriques finales (nouvelle approche Julia)
            'final_metric_entropy': final_analysis.get('metric_entropy', 0.0),
            'final_conditional_entropy': final_analysis.get('conditional_entropy', 0.0),
            'final_entropy_rate': final_analysis.get('entropy_rate', 0.0),
            'final_simple_entropy': final_analysis.get('simple_entropy', 0.0),
            'final_simple_entropy_theoretical': final_analysis.get('simple_entropy_theoretical', 0.0),
            
            # Analyse de complexité
            'complexity_metrics': complexity_metrics,
            
            # Positions d'intérêt
            'max_metric_entropy_position': max(entropy_evolution, key=lambda x: x.get('metric_entropy', 0))['position'] if entropy_evolution else 0,
            'max_conditional_entropy_position': max(entropy_evolution, key=lambda x: x.get('conditional_entropy', 0))['position'] if entropy_evolution else 0
        }

    # ==================== MÉTHODES SUPPLÉMENTAIRES POUR INTÉGRATION ====================

    def calculate_theoretical_entropy_one_based(self) -> float:
        """
        Calcule l'entropie théorique maximale INDEX5
        """
        result = self.Main.eval("JuliaEntropyCore.calculate_theoretical_entropy_one_based()")
        return float(result)

    def calculate_all_metrics_one_based(self, sequence_history: List[str], current_metrics: Dict = None, entropy_evolution: List[Dict] = None) -> Dict:
        """
        Calcule toutes les métriques INDEX5 via Julia
        """
        if len(sequence_history) < 2:
            return {"error": "Séquence trop courte pour calcul métriques"}

        julia_sequence = self._convert_to_julia_vector_string(sequence_history)

        # Convertir current_metrics en Dict Julia
        julia_current_metrics = "Dict()" if current_metrics is None else self._convert_to_julia_dict(current_metrics)

        # Convertir entropy_evolution en Array Julia
        julia_evolution = "[]" if entropy_evolution is None else self._convert_to_julia_array_dict(entropy_evolution)

        try:
            result = self.Main.eval(f"JuliaMetricsCalculator.calculate_all_metrics_one_based({julia_sequence}, {julia_current_metrics}, {julia_evolution})")
            return self._convert_from_julia_dict(result)
        except Exception as e:
            # Retour par défaut en cas d'erreur avec détail
            return {"error": f"Calcul métriques Julia échoué: {str(e)}"}

    def predict_next_index5_one_based(self, sequence_history: List[str], all_metrics: Dict = None) -> Dict:
        """
        Prédiction INDEX5 via Julia
        """
        if len(sequence_history) < 2:
            return {"predicted_index5": "WAIT", "confidence": 0.0, "error": "Séquence trop courte"}

        julia_sequence = self._convert_to_julia_vector_string(sequence_history)

        # Convertir all_metrics en Dict Julia
        julia_metrics = "Dict()" if all_metrics is None else self._convert_to_julia_dict(all_metrics)

        try:
            result = self.Main.eval(f"JuliaPredictionEngine.predict_next_index5_one_based({julia_sequence}, {julia_metrics})")
            return self._convert_from_julia_dict(result)
        except Exception as e:
            # Retour par défaut en cas d'erreur avec détail
            return {"predicted_index5": "WAIT", "confidence": 0.0, "error": f"Prédiction Julia échouée: {str(e)}"}

# ==================== TESTS DE VALIDATION ====================

def test_julia_python_bridge():
    """
    Tests de validation du pont Python-Julia
    Selon plan.txt Phase 1.2
    """
    print("Tests de validation du pont Python-Julia...")

    bridge = AdvancedJuliaPythonBridge()

    # Test indexation 1-based
    test_sequence = ["1_A_BANKER", "0_B_PLAYER", "1_C_TIE"]

    # Test calculs entropiques
    shannon_result = bridge.calculate_shannon_entropy_one_based([0.5, 0.3, 0.2])
    aep_result = bridge.calculate_sequence_entropy_aep_one_based(test_sequence)

    print(f"Shannon entropy: {shannon_result}")
    print(f"AEP entropy: {aep_result}")

    # Test analyse complète
    complete_result = bridge.analyze_complete_game_one_based(test_sequence)
    print(f"Analyse complete: {len(complete_result)} metriques calculees")

    return True

if __name__ == "__main__":
    test_julia_python_bridge()
