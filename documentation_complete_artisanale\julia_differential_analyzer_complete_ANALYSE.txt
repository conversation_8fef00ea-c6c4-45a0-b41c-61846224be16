DOCUMENTATION COMPLÈTE ARTISANALE - julia_differential_analyzer_complete.jl
═══════════════════════════════════════════════════════════════════════════════

📋 INFORMATIONS GÉNÉRALES
═══════════════════════════
• Fichier: julia_differential_analyzer_complete.jl
• Lignes totales: 661
• Type: Module Julia pour analyses différentielles INDEX5
• Priorité: 4 selon plan.txt (lignes 835-839)
• Migré depuis: INDEX5DifferentialAnalyzer (lignes 2302-2709)
• Architecture: Indexation 1-based native Julia
• Qualité: Artisanale - Chaque fichier texte lu intégralement

📊 STRUCTURE DU MODULE
═══════════════════════
Module: JuliaDifferentialAnalyzer

🔧 IMPORTS (lignes 12-15):
- Printf (pour @sprintf)
- JuliaEntropyCore
- JuliaMetricsCalculator
- JuliaPredictionEngine

🔧 CONSTANTES (lignes 18-44):
ALL_INDEX5_VALUES: 18 valeurs INDEX5 possibles (avec règles INDEX1)
DIFFERENTIAL_CACHE: Cache pour éviter recalculs entre tableaux

🔧 EXPORTS (lignes 27-31):
- calculate_differentials_one_based
- calculate_predictive_differentials_one_based
- get_differential_statistics_one_based
- calculate_predictive_score_one_based
- generate_predictive_table_one_based
- generate_predictive_table_part_one_based
- generate_predictive_score_table_one_based
- generate_predictive_score_table_part_one_based
- verify_score_consistency_one_based

📋 MÉTHODES PRINCIPALES ANALYSÉES
═══════════════════════════════════

1. calculate_differentials_one_based(entropy_evolution) - Lignes 57-93
   • Fonction: Calcule les différentiels pour toutes les métriques
   • Source: calculate_differentials.txt (48 lignes)
   • Lignes source: 2303-2345
   
   ALGORITHME:
   a) Validation: Minimum 2 éléments dans entropy_evolution
   b) Première main: Différentiels = 0.0 (pas de main précédente)
   c) Mains suivantes: Différentiels absolus entre main courante et précédente
   
   DIFFÉRENTIELS CALCULÉS:
   • diff_conditional: |conditional_entropy[i] - conditional_entropy[i-1]|
   • diff_entropy_rate: |entropy_rate[i] - entropy_rate[i-1]|
   • diff_simple_entropy: |simple_entropy[i] - simple_entropy[i-1]|
   • diff_simple_entropy_theoretical: |simple_entropy_theoretical[i] - simple_entropy_theoretical[i-1]|
   
   • Retourne: Vector{Dict{String, Any}} avec différentiels par position

2. get_differential_statistics_one_based(differentials) - Lignes 95-140
   • Fonction: Calcule les statistiques sur les différentiels
   • Source: get_differential_statistics.txt (35 lignes)
   • Lignes source: 2347-2376
   
   STATISTIQUES CALCULÉES:
   Pour chaque type de différentiel:
   • Minimum: minimum(values)
   • Maximum: maximum(values)
   • Moyenne: mean(values)
   • Écart-type: std(values)
   
   • Retourne: Dict{String, Dict{String, Float64}} avec statistiques complètes

3. calculate_predictive_differentials_one_based(sequence, evolution, analyzer) - Lignes 142-200
   • Fonction: Calcule les différentiels prédictifs pour les 9 valeurs INDEX5 possibles
   • Source: calculate_predictive_differentials.txt (55 lignes)
   • Lignes source: 2378-2427
   
   ALGORITHME:
   a) Pour chaque position dans la séquence
   b) Pour chaque valeur INDEX5 possible (respectant règles INDEX1)
   c) Simulation: Ajouter la valeur à la position
   d) Calcul: Métriques simulées avec calculate_simulated_metrics_one_based()
   e) Différentiels: Entre métriques simulées et réelles
   
   • Retourne: Dict avec différentiels pour chaque position et valeur INDEX5

4. calculate_predictive_score_one_based(sequence, evolution, analyzer) - Lignes 202-260
   • Fonction: Calcule les scores prédictifs selon formule spécialisée
   • Source: calculate_predictive_score.txt (55 lignes)
   • Lignes source: 2429-2478
   
   FORMULE SCORE:
   • SCORE = (DiffC + EntG) / (DiffT + DivEG)
   • DiffC = DiffCond (Différentiel Entropie Conditionnelle)
   • DiffT = DiffTaux (Différentiel Taux d'Entropie)
   • DivEG = DiffDivEntropG (Différentiel Diversité Entropique)
   • EntG = DiffEntropG (Différentiel Entropie Générale)
   
   GESTION CAS LIMITES:
   • Si dénominateur = 0 → "INF"
   • Si valeur non respecte INDEX1 → "N/A"
   
   • Retourne: Dict avec scores pour chaque position et valeur INDEX5

5. generate_predictive_table_one_based(sequence, evolution, analyzer) - Lignes 262-320
   • Fonction: Génère le tableau prédictif complet avec différentiels
   • Source: generate_predictive_table.txt (55 lignes)
   • Lignes source: 2480-2529
   
   STRUCTURE TABLEAU:
   • En-tête: Main | 0_A_BANKER | 0_A_PLAYER | ... (9 colonnes INDEX5)
   • Corps: Différentiels pour chaque main et valeur INDEX5
   • Format: Valeurs arrondies à 3 décimales
   • Division: Deux parties (Mains 1-30, Mains 31-60)

6. generate_predictive_score_table_one_based(sequence, evolution, analyzer, precomputed_differentials) - Lignes 627-658
   • Fonction: Génère le tableau prédictif complet avec SCORES
   • Source: generate_predictive_score_table.txt (39 lignes)
   • Lignes source: 2563-2596
   
   OPTIMISATION:
   • Utilise différentiels pré-calculés pour synchronisation parfaite
   • Évite recalculs entre tableaux différentiel et scores
   • Cache: DIFFERENTIAL_CACHE pour performance
   
   LÉGENDE INCLUSE:
   • Formule score détaillée
   • Règles INDEX1 déterministes
   • Signification codes (N/A, INF, ---)

🎯 RÈGLES INDEX1 DÉTERMINISTES
═══════════════════════════════
Appliquées dans tous les calculs:
• Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
• Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)

VALEURS INDEX5 EXCLUES:
• 0_C_BANKER (INDEX1=0 avec INDEX2=C impossible)
• 1_C_PLAYER (INDEX1=1 avec INDEX2=C impossible)

📊 ARCHITECTURE DIFFÉRENTIELLE
═══════════════════════════════
• Calculs: 4 types de différentiels pour analyse complète
• Simulation: Métriques prédictives pour 9 valeurs INDEX5 valides
• Scores: Formule spécialisée (DiffC + EntG) / (DiffT + DivEG)
• Tableaux: Division en parties pour lisibilité
• Cache: Optimisation performance avec DIFFERENTIAL_CACHE
• Indexation: 1-based native Julia cohérente

🔧 INTÉGRATION HYBRIDE
═══════════════════════
• Dépendances: 3 autres modules Julia
• Usage: Appelé depuis Python via pont Julia
• Performance: Cache pour éviter recalculs
• Qualité: 407 lignes de fichiers texte lues intégralement

TOTAL: 661 lignes - Module analyses différentielles INDEX5 complet avec 7 méthodes
