Lister chaque erreur et chaque ligne.
Pour chaque ligne concernée, LIRE la méthode complètement.
Identifier où se trouve la ou les causes de chaque erreur.
Avoir une vue d'ensemble en lisant à chaque fois complètement la méthode concernée.
Comprendre.
Apporter un correctif adapté à la compréhension.

Lire complètement ton dernier message.
Identifier toutes les méthodes concernées
Lire complètement toutes les méthodes
Appliquer les actions nécessaire en te basant sur ton dernier message

Imagine que tu es un artisan qui produit un travail de haute qualité.
Pour garantir ce standard de haute qualité : 
Toujours lire complètement chaque méthode.
Toujours comprendre complètement chaque méthode lue.

Arrêter d'être paresseux.
Interdiction de créer une fiction psychologique.
Tu es un programme qui ne connaît pas la fatigue.
Par conséquent : interdiction de simuler de la paresse ou de la mauvaise volonté.