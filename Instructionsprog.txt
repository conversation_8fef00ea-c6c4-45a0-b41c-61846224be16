Lire complètement,un par un chacun de ces fichiers :
 
julia_entropy_core_complete_ANALYSE.txt (618 lignes analysées) 
julia_validation_engine_complete_ANALYSE.txt (333 lignes analysées) 
julia_metrics_calculator_complete_ANALYSE.txt (706 lignes analysées) 
julia_prediction_engine_complete_ANALYSE.txt (635 lignes analysées) 
julia_differential_analyzer_complete_ANALYSE.txt (661 lignes analysées) 

6. main_py_ANALYSE.txt (191 lignes analysées) 
7. python_julia_bridge_py_ANALYSE.txt (345 lignes analysées) 
8. python_reports_py_ANALYSE.txt (384 lignes analysées) 
9. python_configuration_py_ANALYSE.txt (224 lignes analysées)
10. python_data_management_py_ANALYSE.txt (123 lignes analysées) 
11. python_user_interface_py_ANALYSE.txt (347 lignes analysées) 
12. python_visualization_py_ANALYSE.txt (92 lignes analysées) 
13. python_interface_methods_py_ANALYSE.txt (61 lignes analysées)

But : acquérir un compréhension totale de notre programme.

Arrêter d'être paresseux. 
Interdiction de créer une fiction psychologique. 
Tu es un programme qui ne connaît pas la fatigue. 
Par conséquent : interdiction de simuler de la paresse ou de la mauvaise volonté. 
Faire toujours le travail demandé complètement.

Finalement : 
Dans le dossier C:\Users\<USER>\Desktop\9 uniquement :
Lire complètement chaque fichier jl
Lire complètement chaque fichier py

Vérifier si chaque fichier texte est complet.
Sinon compléter.

julia_entropy_core_complete_ANALYSE.txt (618 lignes analysées) 
julia_validation_engine_complete_ANALYSE.txt (333 lignes analysées) 
julia_metrics_calculator_complete_ANALYSE.txt (706 lignes analysées) 
julia_prediction_engine_complete_ANALYSE.txt (635 lignes analysées) 
julia_differential_analyzer_complete_ANALYSE.txt (661 lignes analysées) 

6. main_py_ANALYSE.txt (191 lignes analysées) 
7. python_julia_bridge_py_ANALYSE.txt (345 lignes analysées) 
8. python_reports_py_ANALYSE.txt (384 lignes analysées) 
9. python_configuration_py_ANALYSE.txt (224 lignes analysées)
10. python_data_management_py_ANALYSE.txt (123 lignes analysées) 
11. python_user_interface_py_ANALYSE.txt (347 lignes analysées) 
12. python_visualization_py_ANALYSE.txt (92 lignes analysées) 
13. python_interface_methods_py_ANALYSE.txt (61 lignes analysées)

Doivent être complets.