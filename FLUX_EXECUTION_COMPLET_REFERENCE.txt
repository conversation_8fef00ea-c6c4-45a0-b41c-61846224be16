🎯 FLUX D'EXÉCUTION COMPLET - PROGRAMME RÉFÉRENCE PYTHON
═══════════════════════════════════════════════════════════

📖 ANALYSE COMPLÈTE DE : C:\Users\<USER>\Desktop\9\parallèle\entropie_baccarat_analyzer.py
📊 TOTAL : 3416 lignes - 9 CLASSES PRINCIPALES - ARCHITECTURE MODULAIRE

🔧 1. POINT D'ENTRÉE PRINCIPAL - main() (lignes 3243-3416)
═══════════════════════════════════════════════════════════

FLUX PRINCIPAL :
1. Initialisation BaccaratEntropyAnalyzer()
2. Chargement données JSON : load_baccarat_data()
3. Menu interactif 5 options
4. Exécution selon choix utilisateur

OPTION 1 - ANALYSE PARTIE UNIQUE (FLUX PRINCIPAL) :
→ analyzer.analyze_single_game(data[game_index], f"Partie_{game_index + 1}")
→ analyzer.generate_entropy_report(result)
→ Export automatique rapport{N}.txt
→ Proposition graphique et CSV

🧮 2. CLASSE PRINCIPALE - BaccaratEntropyAnalyzer (lignes 64-1147)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS CENTRALES :
- Moteur d'analyse entropique fondamental
- Calculs entropiques de base (Shannon, AEP, conditionnelle, métrique)
- Analyse de l'évolution entropique par blocs
- Génération de rapports complets

MÉTHODES CLÉS DANS LE FLUX :

2.1 __init__() (lignes 86-122) :
→ Probabilités théoriques INDEX5 EXACTES
→ Calcul entropie théorique : 3.9309 bits
→ Affichage : "🎯 Entropie théorique INDEX5: 3.9309 bits"

2.2 analyze_single_game() (lignes 550-598) :
→ extract_index5_sequence(game_data)
→ calculate_block_entropy_evolution(sequence, max_block_length=4)
→ _calculate_sequence_complexity(sequence)
→ Retourne dictionnaire complet avec entropy_evolution

2.3 calculate_block_entropy_evolution() (lignes 277-339) :
BOUCLE PRINCIPALE pour chaque position n :
→ subsequence = sequence[:n]
→ _calculate_block_entropies(subsequence, max_block_length)
→ _calculate_conditional_entropy(subsequence)
→ _estimate_metric_entropy(subsequence, max_block_length)
→ _calculate_sequence_entropy_aep(subsequence) [FORMULE AEP UNIFIÉE]

2.4 _calculate_sequence_entropy_aep() (lignes 415-453) :
FORMULE AEP CENTRALE :
H_séquence = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)
où p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)

2.5 generate_entropy_report() (lignes 771-1072) :
GÉNÉRATION RAPPORT COMPLET :
→ Initialisation INDEX5Calculator(analyzer=self)
→ Initialisation INDEX5Predictor()
→ Initialisation INDEX5PredictionValidator()
→ Initialisation INDEX5DifferentialAnalyzer()
→ Calcul différentiels : differential_analyzer.calculate_differentials(evolution)
→ BOUCLE PRÉDICTIONS : predictor.predict_next_index5(sequence_up_to_i, current_metrics)
→ BOUCLE MÉTRIQUES : calculator.calculate_all_metrics(sequence_up_to_i, current_metrics, evolution[:i+1])
→ GÉNÉRATION TABLEAUX PRÉDICTIFS

📊 3. CALCULATEUR DE MÉTRIQUES - INDEX5Calculator (lignes 1167-1717)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- 12 algorithmes de métriques avancées INDEX5
- Analyse de prédictibilité contextuelle
- Calculs de stabilité et complexité

MÉTHODE CLÉ DANS LE FLUX :

3.1 calculate_all_metrics() (lignes 1669-1716) :
CALCUL 12 MÉTRIQUES :
1. context_predictability
2. pattern_strength
3. entropy_stability
4. compression_score
5. structural_richness
6. bayesian_divergence
7. conditional_entropy_context
8. multi_algorithm_consensus
9. deterministic_pattern_score
10. bayesian_theoretical_alignment
11. transition_matrix_entropy
12. frequency_stability

🔮 4. SYSTÈME DE PRÉDICTION - INDEX5Predictor (lignes 1737-2264)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Système de prédiction multi-algorithmes
- Fusion de 3 algorithmes (déterministe, bayésien, fréquentiel)
- Respect des contraintes INDEX1 obligatoires

MÉTHODE CLÉ DANS LE FLUX :

4.1 predict_next_index5() (lignes 2049-2136) :
ALGORITHME FUSION MULTI-ALGORITHMES :
→ calculate_required_index1(current_index5) [RÈGLES DÉTERMINISTES]
→ get_valid_index5_values(required_index1) [9 VALEURS POSSIBLES]
→ predict_deterministic_patterns() [ALGORITHME 1]
→ predict_bayesian_theoretical() [ALGORITHME 2]
→ predict_frequency_based() [ALGORITHME 3]
→ FILTRAGE par contraintes INDEX1
→ VOTE PONDÉRÉ selon prédictibilité
→ Retourne prédiction finale ou "WAIT"

📈 5. ANALYSEUR DIFFÉRENTIEL - INDEX5DifferentialAnalyzer (lignes 2281-2377)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Calcul des différentiels entre mains consécutives
- Support pour les tableaux prédictifs

MÉTHODE CLÉ DANS LE FLUX :

5.1 calculate_differentials() (lignes 2303-2345) :
CALCUL DIFFÉRENTIELS :
→ diff_conditional = |current.conditional_entropy - previous.conditional_entropy|
→ diff_entropy_rate = |current.entropy_rate - previous.entropy_rate|
→ diff_simple_entropy = |current.simple_entropy - previous.simple_entropy|
→ diff_simple_entropy_theoretical = |current.simple_entropy_theoretical - previous.simple_entropy_theoretical|

📋 6. GÉNÉRATEURS DE TABLEAUX - INDEX5PredictiveDifferentialTable (lignes 2646-2996)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Génération tableaux prédictifs avec différentiels
- Application règles INDEX1 déterministes

MÉTHODE CLÉ DANS LE FLUX :

6.1 generate_predictive_table() (lignes 2966-2996) :
→ generate_predictive_table_part(sequence, evolution, analyzer, 1, 30, 1)
→ generate_predictive_table_part(sequence, evolution, analyzer, 31, 60, 2)
→ Combinaison avec légende complète

6.2 calculate_predictive_differentials() (lignes 2758-2861) :
ALGORITHME PRÉDICTIF :
→ calculate_required_index1(reference_index5) [MAIN n-1]
→ get_valid_index5_values(required_index1) [9 POSSIBILITÉS]
→ Pour chaque valeur possible :
  - Simulation séquence avec valeur ajoutée
  - Calcul métriques simulées
  - Calcul différentiels |simulé - actuel|

🎯 7. CALCULATEURS DE SCORES - INDEX5PredictiveScoreTable (lignes 2431-2627)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Calcul scores prédictifs selon formule composite
- SCORE = (DiffC + EntG) / (DiffT + DivEG)

MÉTHODE CLÉ DANS LE FLUX :

7.1 generate_predictive_score_table() (lignes 2563-2596) :
→ Utilise precomputed_differentials du cache
→ calculate_predictive_score() pour chaque cellule
→ Génération tableau avec SCORES au lieu des différentiels

✅ 8. VALIDATEUR DE PRÉDICTIONS - INDEX5PredictionValidator (lignes 3017-3221)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Validation prédictions par comparaison INDEX3
- Statistiques de précision

MÉTHODE CLÉ DANS LE FLUX :

8.1 validate_prediction() (lignes 3082-3134) :
→ extract_index3(predicted_index5)
→ extract_index3(actual_index5)
→ RÈGLE TIE : Si réalité=TIE et prédiction≠TIE → NE PAS COMPTER
→ Compteurs précision globale et haute confiance

🔄 FLUX COMPLET D'EXÉCUTION POUR UNE PARTIE :
═══════════════════════════════════════════════════════════

1. main() → Choix option 1
2. analyzer.analyze_single_game()
   ├── extract_index5_sequence()
   ├── calculate_block_entropy_evolution()
   │   └── Pour chaque position : _calculate_sequence_entropy_aep()
   └── _calculate_sequence_complexity()
3. analyzer.generate_entropy_report()
   ├── Initialisation 4 classes : Calculator, Predictor, Validator, DifferentialAnalyzer
   ├── differential_analyzer.calculate_differentials()
   ├── BOUCLE PRÉDICTIONS :
   │   ├── calculator.calculate_all_metrics()
   │   └── predictor.predict_next_index5()
   ├── BOUCLE VALIDATION :
   │   └── validator.validate_prediction()
   ├── GÉNÉRATION TABLEAUX :
   │   ├── INDEX5PredictiveDifferentialTable.generate_predictive_table()
   │   └── INDEX5PredictiveScoreTable.generate_predictive_score_table()
   └── validator.get_detailed_report()
4. Export automatique rapport{N}.txt
5. Proposition graphique et CSV













═══════════════════════════════════════════════════════════
═══════════════════════════════════════════════════════════
═══════════════════════════════════════════════════════════
DELIMITATION ENTRE FLUX DE L'ANCIEN PROGRAMME ET FLUXS du NOUVEAU PROGRAMME
═══════════════════════════════════════════════════════════
═══════════════════════════════════════════════════════════
═══════════════════════════════════════════════════════════










 RAPPORT COMPLET AVEC TOUTES LES ANALYSES
═══════════════════════════════════════════════════════════

🔧 FLUX D'EXÉCUTION JULIA_ENTROPY_CORE_COMPLETE.JL (618 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : 15 fonctions entropiques fondamentales
ARCHITECTURE : Module Julia avec indexation 1-based

MÉTHODES CLÉS DANS LE FLUX :

1. PROBABILITÉS THÉORIQUES (lignes 21-39) :
→ THEORETICAL_PROBS_RAW_ONE_BASED : Probabilités brutes INDEX5
→ THEORETICAL_PROBS_ONE_BASED : Probabilités normalisées
→ CORRECTION EXPERTE : 0_B_BANKER = 6.4676% (était 7.6907%)

2. FONCTIONS DE BASE (lignes 51-112) :
→ safe_log_one_based() : Calcul sécurisé logarithme avec epsilon
→ validate_probabilities_one_based() : Validation et normalisation
→ calculate_shannon_entropy_one_based() : H(X) = -∑ p(x) log₂ p(x)

3. FORMULE AEP CENTRALE (lignes 125-153) :
→ calculate_sequence_entropy_aep_one_based() : MÉTHODE MAÎTRE
→ H_séquence = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)
→ p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)

4. ÉVOLUTION ENTROPIQUE (lignes 205-258) :
→ calculate_block_entropy_evolution_one_based() : MÉTHODE CENTRALE
→ BOUCLE POSITION PAR POSITION (indexation 1-based)
→ Pour chaque n : subsequence = sequence[1:n]
→ Calculs : block_entropies, conditional_entropy, metric_entropy
→ Retourne Vector{Dict{String, Any}} avec toutes les métriques

5. ENTROPIE CONDITIONNELLE (lignes 270-321) :
→ calculate_conditional_entropy_one_based() : H(Xₙ|Xₙ₋₁)
→ CORRECTION EXPERTE : Contexte de longueur fixe
→ Utilise formule AEP pour chaque contexte

6. ENTROPIE MÉTRIQUE (lignes 333-364) :
→ estimate_metric_entropy_one_based() : h_μ(T) Kolmogorov-Sinai
→ CORRECTION EXPERTE : h_μ = lim_{n→∞} H(n)/n
→ Calcul rigoureux de la limite

7. ENTROPIES DE BLOCS (lignes 375-457) :
→ calculate_block_entropies_raw_one_based() : SANS normalisation
→ calculate_block_entropies_one_based() : AVEC normalisation
→ CORRECTION AEP : Utilise formule AEP pour tous les blocs

8. COMPLEXITÉ DE SÉQUENCE (lignes 466-493) :
→ calculate_sequence_complexity_one_based() : Métriques diverses
→ Motifs uniques, complexité LZ, entropie topologique

9. ALGORITHMES AVANCÉS (lignes 502-615) :
→ approximate_lz_complexity_one_based() : Complexité Lempel-Ziv
→ approximate_topological_entropy_one_based() : h_top ≥ h_μ
→ calculate_repetition_rate_one_based() : Taux de répétition

FLUX PRINCIPAL D'UTILISATION :
1. calculate_block_entropy_evolution_one_based() appelée depuis Python
2. Pour chaque position n, calcule toutes les métriques entropiques
3. Retourne évolution complète position par position
4. Utilisé par python_julia_bridge.py ligne 287

POINTS CRITIQUES :
- Indexation 1-based native Julia respectée partout
- Formule AEP unifiée pour tous les calculs
- Probabilités théoriques INDEX5 exactes avec correction
- Calculs rigoureux selon théorie de l'information

🔧 FLUX D'EXÉCUTION JULIA_VALIDATION_ENGINE_COMPLETE.JL (333 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : 7 fonctions validation INDEX5 avec règles TIE critiques
ARCHITECTURE : Module Julia avec indexation 1-based

MÉTHODES CLÉS DANS LE FLUX :

1. STRUCTURE VALIDATION (lignes 24-35) :
→ ValidationStats : Structure mutable pour statistiques
→ VALIDATION_STATS : Instance globale
→ Compteurs : correct_predictions, total_predictions, high_confidence

2. EXTRACTION INDEX3 (lignes 44-68) :
→ extract_index3_one_based() : INDEX1_INDEX2_INDEX3 → INDEX3
→ Normalisation : BANK → BANKER, PLAY → PLAYER
→ Nettoyage automatique des scores de confiance

3. EXTRACTION CONFIANCE (lignes 77-89) :
→ extract_confidence_one_based() : INDEX5(0.XX) → 0.XX
→ Parsing sécurisé avec try-catch

4. VALIDATION PRINCIPALE (lignes 98-153) :
→ validate_prediction_one_based() : MÉTHODE CENTRALE
→ RÈGLE TIE CRITIQUE : Si réalité=TIE et prédiction≠TIE → NE PAS COMPTER
→ Seuil haute confiance : >= 0.60 (60% poids pondéré)
→ Mise à jour automatique des compteurs globaux

5. STATISTIQUES (lignes 160-192) :
→ get_accuracy_stats_one_based() : Calcul précision globale et haute confiance
→ Retourne dictionnaire complet avec ratios et pourcentages

6. RAPPORT DÉTAILLÉ (lignes 199-247) :
→ get_detailed_report_one_based() : Rapport formaté complet
→ Affichage des 10 dernières prédictions
→ Symboles visuels : ✅❌ pour résultats, 🎯📊 pour confiance

7. CONTRAINTES INDEX1 (lignes 272-306) :
→ apply_index1_constraint_one_based() : Règles déterministes
→ Si INDEX2=C : INDEX1 s'inverse (0→1, 1→0)
→ Si INDEX2=A ou B : INDEX1 se conserve (0→0, 1→1)

FLUX PRINCIPAL D'UTILISATION :
1. validate_prediction_one_based() appelée pour chaque prédiction
2. Accumulation automatique des statistiques dans VALIDATION_STATS
3. get_detailed_report_one_based() pour rapport final
4. Utilisé par python_reports.py pour validation des prédictions

POINTS CRITIQUES :
- Règle TIE strictement appliquée (ne pas compter si réalité=TIE et prédiction≠TIE)
- Seuil haute confiance à 60% selon spécifications
- Indexation 1-based pour extraction des parties INDEX5
- Contraintes INDEX1 déterministes appliquées

🔧 FLUX D'EXÉCUTION JULIA_METRICS_CALCULATOR_COMPLETE.JL (706 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : 16 algorithmes métriques spécialisés INDEX5
ARCHITECTURE : Module Julia avec indexation 1-based

MÉTHODES CLÉS DANS LE FLUX :

1. PRÉDICTIBILITÉ CONTEXTUELLE (lignes 31-54) :
→ calculate_context_predictability_one_based() : Score basé entropie conditionnelle
→ Normalisation corrigée : (6.2192 - conditional_entropy) / 6.2192
→ Score composite : 50% entropie + 30% patterns + 20% répétition

2. FORCE DES PATTERNS (lignes 86-111) :
→ calculate_pattern_strength_one_based() : Patterns récurrents longueur 2-5
→ Force = occurrences × (longueur_pattern / 5.0)
→ Retourne score maximum des patterns trouvés

3. STABILITÉ ENTROPIQUE (lignes 120-141) :
→ calculate_entropy_stability_score_one_based() : Variance entropie métrique
→ Score = 1.0 / (1.0 + variance × 10)
→ Plus stable = score plus élevé

4. COMPRESSION (lignes 150-174) :
→ calculate_compression_score_one_based() : Basé complexité LZ
→ Score = 1.0 - (lz_complexity / max_complexity)
→ Plus compressible = score plus élevé

5. RICHESSE STRUCTURELLE (lignes 183-224) :
→ calculate_structural_richness_score_one_based() : Entropie topologique + diversité
→ Score composite : 60% topologique + 40% diversité patterns
→ Normalisation sur entropie max théorique 4.17

6. DIVERGENCE BAYÉSIENNE (lignes 233-261) :
→ calculate_bayesian_divergence_score_one_based() : Divergence KL observé/théorique
→ D_KL(P_obs || P_theo) = Σ P_obs(x) log(P_obs(x) / P_theo(x))
→ Normalisation sigmoïde : 2 / (1 + exp(-kl_divergence)) - 1

7. ENTROPIE CONDITIONNELLE CONTEXTUELLE (lignes 270-316) :
→ calculate_conditional_entropy_context_one_based() : H(X|Context) longueur variable
→ CORRECTION AEP : Utilise formule AEP pour chaque contexte
→ Pondération par probabilité du contexte

8. CONSENSUS MULTI-ALGORITHMES (lignes 326-363) :
→ calculate_multi_algorithm_consensus_score_one_based() : Accord entre méthodes
→ Calcule variance des scores : faible variance = consensus élevé
→ Score = 1.0 / (1.0 + variance × 10)

9. PATTERNS DÉTERMINISTES (lignes 372-407) :
→ calculate_deterministic_pattern_score_one_based() : Récurrence patterns
→ Score = (fréquence_max / total) × poids_longueur
→ Retourne meilleur pattern déterministe

10. ALIGNEMENT BAYÉSIEN (lignes 438-485) :
→ calculate_bayesian_theoretical_alignment_one_based() : Observations vs théorie
→ Score = 1 - différence_relative pondéré par probabilités théoriques
→ Analyse 20 dernières observations

11. ENTROPIE MATRICE TRANSITIONS (lignes 494-539) :
→ calculate_transition_matrix_entropy_one_based() : H(transitions)
→ CORRECTION AEP : Entropie AEP pour chaque état
→ Pondération par fréquence des états

12. STABILITÉ FRÉQUENCES (lignes 548-588) :
→ calculate_frequency_stability_score_one_based() : Stabilité globale vs récente
→ Score = 1 - différence_relative entre fréquences globales et récentes
→ Analyse 30 dernières vs historique complet

13. CALCUL TOUTES MÉTRIQUES (lignes 597-643) :
→ calculate_all_metrics_one_based() : MÉTHODE CENTRALE
→ Calcule les 12 métriques principales
→ Retourne dictionnaire complet

14. MÉTRIQUES SIMULÉES (lignes 650-703) :
→ calculate_simulated_metrics_one_based() : Pour tableaux prédictifs
→ Simule ajout d'une valeur et calcule métriques résultantes
→ Utilisé pour différentiels prédictifs

FLUX PRINCIPAL D'UTILISATION :
1. calculate_all_metrics_one_based() appelée depuis Python
2. Calcule 12 métriques spécialisées pour position donnée
3. Utilisé dans boucles prédictions du programme référence
4. calculate_simulated_metrics_one_based() pour tableaux prédictifs

POINTS CRITIQUES :
- Formule AEP utilisée partout pour cohérence
- Normalisation corrigée basée sur plages observées
- Probabilités théoriques INDEX5 exactes utilisées
- Indexation 1-based native Julia respectée

🔧 FLUX D'EXÉCUTION JULIA_PREDICTION_ENGINE_COMPLETE.JL (635 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : 21 algorithmes prédiction avec fusion multi-algorithmes
ARCHITECTURE : Module Julia avec indexation 1-based

MÉTHODES CLÉS DANS LE FLUX :

1. PRÉDICTEUR PRINCIPAL (lignes 34-126) :
→ predict_next_index5_one_based() : MÉTHODE CENTRALE FUSION
→ ÉTAPE 1 : calculate_required_index1_one_based() - Règles déterministes
→ ÉTAPE 2 : get_valid_index5_values_one_based() - 9 valeurs possibles
→ ÉTAPE 3 : Évaluation prédictibilité : (6.2192 - conditional_entropy) / 6.2192
→ ÉTAPE 4 : Sélection stratégie selon prédictibilité (40%, 30%, <30%)
→ ÉTAPE 5 : Calcul 3 prédictions : déterministe, bayésienne, fréquentielle
→ ÉTAPE 6 : FILTRAGE par contraintes INDEX1
→ ÉTAPE 7 : Vote pondéré sur prédictions valides
→ ÉTAPE 8 : Retourne prédiction finale ou "WAIT"

2. ALGORITHME DÉTERMINISTE (lignes 133-161) :
→ predict_deterministic_patterns_one_based() : Exploitation patterns récurrents
→ Analyse patterns longueur 2-5
→ Pondération : fréquence × (1.0 / longueur_pattern)
→ Retourne pattern le plus fréquent

3. ALGORITHME BAYÉSIEN (lignes 168-198) :
→ predict_bayesian_theoretical_one_based() : Fusion observations/théorie
→ Analyse 20 dernières observations
→ Lissage Laplace : (count + 1) / (total + nb_valeurs)
→ Fusion adaptative selon prédictibilité

4. ALGORITHME FRÉQUENTIEL (lignes 205-219) :
→ predict_frequency_based_one_based() : Basé fréquences récentes
→ Analyse 30 dernières observations
→ Retourne valeur la plus fréquente

5. CONTRAINTES INDEX1 (lignes 454-472) :
→ calculate_required_index1_one_based() : RÈGLES DÉTERMINISTES
→ Si INDEX2=C : INDEX1 s'inverse (0→1, 1→0)
→ Si INDEX2=A ou B : INDEX1 se conserve (0→0, 1→1)

6. VALEURS VALIDES (lignes 479-491) :
→ get_valid_index5_values_one_based() : 9 valeurs respectant INDEX1
→ Pour chaque INDEX2 (A,B,C) et INDEX3 (BANKER,PLAYER,TIE)
→ Avec INDEX1 obligatoire calculé

7. FILTRAGE CONTRAINTES (lignes 438-443) :
→ filter_prediction_by_constraint_one_based() : Validation prédiction
→ Retourne prédiction si dans valeurs valides, Nothing sinon

8. ALGORITHMES AVANCÉS (lignes 228-380) :
→ predict_entropy_level_one_based() : Analyse entropique avancée
→ predict_context_level_one_based() : Analyse contextuelle temporelle
→ predict_bayesian_level_one_based() : Bayésien avec probabilités conditionnelles
→ predict_deterministic_model_one_based() : Modèle déterministe transitions
→ predict_compression_patterns_one_based() : Exploitation patterns compression
→ predict_rich_structure_model_one_based() : Modèle sophistiqué multi-patterns

9. UTILITAIRES (lignes 498-580) :
→ find_exact_pattern_continuation_one_based() : Continuations exactes
→ is_metric_entropy_stable_one_based() : Stabilité entropique
→ calculate_conditional_probabilities_one_based() : Probabilités conditionnelles

FLUX PRINCIPAL D'UTILISATION :
1. predict_next_index5_one_based() appelée depuis Python
2. Calcul INDEX1 obligatoire selon règles déterministes
3. Fusion 3 algorithmes avec pondération adaptative
4. Filtrage par contraintes INDEX1
5. Vote pondéré et retour prédiction finale

POINTS CRITIQUES :
- Contraintes INDEX1 appliquées AVANT le vote (filtrage strict)
- Fusion multi-algorithmes avec pondération selon prédictibilité
- Retourne "WAIT" si aucune prédiction valide
- Indexation 1-based native Julia respectée partout

🔧 FLUX D'EXÉCUTION JULIA_DIFFERENTIAL_ANALYZER_COMPLETE.JL (661 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : 9 fonctions analyses différentielles avec formule SCORE
ARCHITECTURE : Module Julia avec indexation 1-based

MÉTHODES CLÉS DANS LE FLUX :

1. DIFFÉRENTIELS DE BASE (lignes 57-93) :
→ calculate_differentials_one_based() : Différentiels entre mains consécutives
→ diff_conditional = |current.conditional_entropy - previous.conditional_entropy|
→ diff_entropy_rate = |current.entropy_rate - previous.entropy_rate|
→ diff_simple_entropy = |current.simple_entropy - previous.simple_entropy|
→ diff_simple_entropy_theoretical = |current.simple_entropy_theoretical - previous.simple_entropy_theoretical|

2. STATISTIQUES DIFFÉRENTIELS (lignes 106-134) :
→ get_differential_statistics_one_based() : Min, max, moyenne, écart-type
→ Exclut première main (différentiels = 0)
→ Calcul variance et écart-type pour chaque métrique

3. SCORE PRÉDICTIF (lignes 150-158) :
→ calculate_predictive_score_one_based() : FORMULE CENTRALE
→ SCORE = (DiffC + EntG) / (DiffT + DivEG)
→ Retourne Inf si dénominateur = 0

4. DIFFÉRENTIELS PRÉDICTIFS (lignes 177-228) :
→ calculate_predictive_differentials_one_based() : MÉTHODE CENTRALE
→ Détermine INDEX1 obligatoire selon règles déterministes
→ Obtient 9 valeurs INDEX5 valides
→ Pour chaque valeur : simule ajout et calcule différentiels
→ Utilise DIFFERENTIAL_CACHE pour éviter recalculs

5. TABLEAU PRÉDICTIF COMPLET (lignes 243-272) :
→ generate_predictive_table_one_based() : Tableau divisé en 2 parties
→ Partie 1 : Mains 6-30 (commence à main 6)
→ Partie 2 : Mains 31-60
→ Légende complète avec règles INDEX1

6. TABLEAU PRÉDICTIF PARTIE (lignes 290-402) :
→ generate_predictive_table_part_one_based() : Formatage avec séparateurs
→ En-têtes : |Main X| avec |DiffC|DiffT|DivEG|EntG|
→ Pour chaque INDEX5 : calcul différentiels prédictifs
→ Ligne OBSERVÉ avec valeurs réelles
→ Séparateur entre 0_C_TIE et 1_A_BANKER

7. TABLEAU SCORES PARTIE (lignes 490-609) :
→ generate_predictive_score_table_part_one_based() : SCORES au lieu différentiels
→ Utilise différentiels pré-calculés pour synchronisation
→ Calcule SCORE pour chaque cellule
→ Format : INF si dénominateur = 0, sinon %.4f

8. TABLEAU SCORES COMPLET (lignes 627-658) :
→ generate_predictive_score_table_one_based() : Tableau scores divisé en 2 parties
→ Utilise différentiels pré-calculés pour garantir synchronisation
→ Légende avec formule SCORE complète

9. VÉRIFICATION COHÉRENCE (lignes 419-445) :
→ verify_score_consistency_one_based() : Validation calculs
→ Compare score calculé vs score manuel
→ Retourne détails vérification avec flag match

FLUX PRINCIPAL D'UTILISATION :
1. calculate_differentials_one_based() pour différentiels de base
2. calculate_predictive_differentials_one_based() pour tableaux prédictifs
3. generate_predictive_table_one_based() pour tableau différentiels
4. generate_predictive_score_table_one_based() pour tableau scores
5. Cache DIFFERENTIAL_CACHE évite recalculs entre tableaux

POINTS CRITIQUES :
- Formule SCORE : (DiffC + EntG) / (DiffT + DivEG)
- Contraintes INDEX1 appliquées (9 valeurs possibles seulement)
- Cache pour synchronisation parfaite entre tableaux
- Indexation 1-based native Julia respectée
- Commence à main 6 pour premier tableau (données suffisantes)

🔧 FLUX D'EXÉCUTION MAIN.PY (191 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : Orchestration centrale avec 3 classes hybrides
ARCHITECTURE : Point d'entrée Python avec délégation Julia

MÉTHODES CLÉS DANS LE FLUX :

1. CLASSE HYBRIDE PRINCIPALE (lignes 27-104) :
→ HybridBaccaratEntropyAnalyzer : Remplace BaccaratEntropyAnalyzer
→ __init__() : Initialise julia_bridge + probabilités théoriques
→ analyze_single_game() : DÉLÉGATION COMPLÈTE à Julia (ligne 85)
→ generate_entropy_report() : Reste en Python (ligne 91)

2. CLASSE HYBRIDE CALCULATEUR (lignes 105-124) :
→ HybridINDEX5Calculator : Remplace INDEX5Calculator
→ calculate_all_metrics() : DÉLÉGATION COMPLÈTE à Julia (lignes 122-124)

3. CLASSE HYBRIDE PRÉDICTEUR (lignes 126-144) :
→ HybridINDEX5Predictor : Remplace INDEX5Predictor
→ predict_next_index5() : DÉLÉGATION COMPLÈTE à Julia (lignes 142-144)

4. POINT D'ENTRÉE PRINCIPAL (lignes 146-171) :
→ main() : Initialise HybridBaccaratEntropyAnalyzer
→ Lance user_main() pour interface utilisateur
→ Gestion erreurs avec message Julia

FLUX PRINCIPAL D'UTILISATION :
1. main() appelée au démarrage
2. Initialisation HybridBaccaratEntropyAnalyzer avec julia_bridge
3. Lancement interface utilisateur Python
4. Toutes les analyses déléguées à Julia via le pont

POINTS CRITIQUES :
- DÉLÉGATION COMPLÈTE : Tous calculs en Julia
- Classes hybrides remplacent classes Python originales
- Interface utilisateur reste en Python
- Pont automatique gère conversions indexation

🔧 FLUX D'EXÉCUTION PYTHON_JULIA_BRIDGE.PY (382 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : Pont automatique avec conversions bidirectionnelles
ARCHITECTURE : Classe AdvancedJuliaPythonBridge

MÉTHODES CLÉS DANS LE FLUX :

1. INITIALISATION (lignes 28-84) :
→ __init__() : Charge TOUS les 5 modules Julia
→ Configuration PATH Julia pour libnghttp2-14.dll
→ Chargement séquentiel : EntropyCore, MetricsCalculator, PredictionEngine, DifferentialAnalyzer, ValidationEngine
→ Gestion erreurs avec try-catch

2. CONVERSIONS TYPES (lignes 86-149) :
→ _convert_to_julia_vector_string() : List[str] → Vector{String}
→ _convert_to_julia_vector_float() : List[float] → Vector{Float64}
→ _convert_from_julia_dict() : Dict Julia → Dict Python
→ convert_to_julia_dict() : Dict Python → Dict Julia

3. MÉTHODES ENTROPIQUES (lignes 153-275) :
→ safe_log_one_based() : Appel JuliaEntropyCore.safe_log_one_based()
→ calculate_shannon_entropy_one_based() : Appel Shannon
→ calculate_sequence_entropy_aep_one_based() : FORMULE MAÎTRE AEP
→ calculate_conditional_entropy_one_based() : Entropie conditionnelle
→ calculate_block_entropy_evolution_one_based() : MÉTHODE CENTRALE

4. ANALYSE COMPLÈTE (lignes 278-316) :
→ analyze_complete_game_one_based() : MÉTHODE PRINCIPALE
→ Appelle calculate_block_entropy_evolution_one_based()
→ Calcule complexity_metrics
→ Retourne dictionnaire complet avec entropy_evolution

5. MÉTHODES SUPPLÉMENTAIRES (lignes 320-351) :
→ calculate_theoretical_entropy_one_based() : Entropie théorique
→ calculate_all_metrics_one_based() : Délégation JuliaMetricsCalculator
→ predict_next_index5_one_based() : Délégation JuliaPredictionEngine

FLUX PRINCIPAL D'UTILISATION :
1. Initialisation charge tous modules Julia
2. analyze_complete_game_one_based() appelée depuis main.py
3. Conversions automatiques Python ↔ Julia
4. Retour résultats convertis en Python

POINTS CRITIQUES :
- Pont automatique entre indexation 0-based Python et 1-based Julia
- Chargement de TOUS les 5 modules Julia requis
- Conversions types seamless
- Gestion erreurs complète avec fallbacks

🔧 FLUX D'EXÉCUTION PYTHON_CONFIGURATION.PY (2233 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : 8 classes configuration avec probabilités exactes
ARCHITECTURE : Classes Python avec délégation Julia

MÉTHODES CLÉS DANS LE FLUX :

1. CLASSE PRINCIPALE BaccaratEntropyAnalyzer (lignes 27-1107) :
→ __init__() : Probabilités théoriques INDEX5 exactes avec correction
→ analyze_single_game() : DÉLÉGATION COMPLÈTE à Julia via julia_bridge
→ generate_entropy_report() : Génération rapport complet (lignes 249-545)
→ calculate_block_entropy_evolution() : Évolution entropique position par position
→ _calculate_sequence_entropy_aep() : FORMULE MAÎTRE AEP unifiée

2. CLASSE INDEX5Calculator (lignes 1108-1641) :
→ calculate_all_metrics() : DÉLÉGATION COMPLÈTE à Julia (lignes 1530-1566)
→ calculate_context_predictability() : Score prédictibilité contextuelle
→ calculate_pattern_strength() : Force patterns récurrents
→ 12 métriques spécialisées avec mode dégradé si Julia échoue

3. CLASSE INDEX5Predictor (lignes 1642-1755) :
→ predict_next_index5() : DÉLÉGATION COMPLÈTE à Julia (lignes 1667-1703)
→ predict_context_level() : Analyse contextuelle temporelle
→ find_exact_pattern_continuation() : Continuations patterns exacts

4. CLASSE INDEX5DifferentialAnalyzer (lignes 1756-1844) :
→ calculate_differentials() : Différentiels entre mains consécutives
→ get_differential_statistics() : Statistiques min/max/moyenne/écart-type
→ PYTHON PUR : Pas de délégation Julia (fonctionne)

5. CLASSE INDEX5PredictiveScoreCalculator (lignes 1846-1881) :
→ calculate_predictive_score() : Formule SCORE = (DiffC + EntG) / (DiffT + DivEG)
→ Retourne Inf si dénominateur = 0

6. CLASSE INDEX5PredictiveScoreTable (lignes 1883-1996) :
→ generate_predictive_score_table() : DÉLÉGATION COMPLÈTE à Julia
→ generate_predictive_score_table_part() : Partie tableau scores
→ Utilise différentiels pré-calculés pour synchronisation

7. CLASSE INDEX5PredictiveDifferentialTable (lignes 1998-2118) :
→ generate_predictive_table() : DÉLÉGATION COMPLÈTE à Julia
→ generate_predictive_table_part() : Partie tableau différentiels
→ Cache _differential_cache pour éviter recalculs

8. CLASSE INDEX5PredictionValidator (lignes 2120-2210) :
→ validate_prediction() : Validation prédiction vs réalité
→ extract_confidence() : Extraction score confiance INDEX5(0.XX)
→ extract_index3() : Extraction INDEX3 depuis INDEX5
→ get_detailed_report() : Rapport validation (mode dégradé)

FLUX PRINCIPAL D'UTILISATION :
1. BaccaratEntropyAnalyzer.analyze_single_game() délègue à Julia
2. generate_entropy_report() orchestre toutes les classes
3. Chaque classe délègue ses calculs lourds à Julia
4. Mode dégradé si Julia échoue

POINTS CRITIQUES :
- DÉLÉGATION MASSIVE : Tous calculs lourds en Julia
- Probabilités INDEX5 exactes avec correction 0_B_BANKER = 6.4676%
- Formule AEP unifiée pour tous calculs entropiques
- Mode dégradé complet si Julia indisponible
- Cache différentiels pour synchronisation tableaux

🔧 FLUX D'EXÉCUTION PYTHON_DATA_MANAGEMENT.PY (123 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : Chargement JSON et extraction INDEX5
ARCHITECTURE : Classe BaccaratDataManager Python pur

MÉTHODES CLÉS DANS LE FLUX :

1. CHARGEMENT DONNÉES (lignes 23-60) :
→ load_baccarat_data() : Chargement fichier JSON avec gestion erreurs
→ Gère 2 structures : {"parties_condensees": [...]} ou [partie1, partie2, ...]
→ Validation structure et comptage parties

2. EXTRACTION SÉQUENCE (lignes 62-105) :
→ extract_index5_sequence() : Extraction INDEX5 avec filtrage
→ Gère 2 structures : {"hands": [...]} ou {"mains_condensees": [...]}
→ Filtre mains d'ajustement (main_number null ou INDEX5 vide)
→ Retourne séquence propre pour analyse

FLUX PRINCIPAL D'UTILISATION :
1. load_baccarat_data() charge fichier JSON
2. extract_index5_sequence() extrait séquence pour chaque partie
3. Données prêtes pour analyse entropique

POINTS CRITIQUES :
- PYTHON PUR : Pas de délégation Julia
- Gestion robuste structures JSON multiples
- Filtrage automatique mains d'ajustement
- Validation et comptage systématiques

🔧 FLUX D'EXÉCUTION PYTHON_INTERFACE_METHODS.PY (61 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : Méthodes homonymes Python-Julia
ARCHITECTURE : Classe PythonInterfaceMethods Python pur

MÉTHODES CLÉS DANS LE FLUX :

1. INTERFACE HOMONYME (lignes 22-44) :
→ get_valid_index5_values() : Retourne 9 valeurs INDEX5 avec INDEX1 obligatoire
→ Pour chaque INDEX2 (A,B,C) et INDEX3 (BANKER,PLAYER,TIE)
→ Format : f"{required_index1}_{index2}_{index3}"

FLUX PRINCIPAL D'UTILISATION :
1. Interface utilisateur appelle get_valid_index5_values()
2. Retourne liste des valeurs possibles selon INDEX1

POINTS CRITIQUES :
- PYTHON PUR : Méthode homonyme pour interface
- Délégation Julia pour calculs via pont automatique
- Interface simple pour utilisateur

🔧 FLUX D'EXÉCUTION PYTHON_REPORTS.PY (386 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : Génération rapports complets
ARCHITECTURE : Classe BaccaratReportGenerator avec délégation Julia

MÉTHODES CLÉS DANS LE FLUX :

1. GÉNÉRATION RAPPORT PRINCIPAL (lignes 29-341) :
→ generate_entropy_report() : Rapport complet 249 lignes intégrées
→ Initialise 4 classes : Calculator, Predictor, Validator, DifferentialAnalyzer
→ Boucle prédictions avec délégation Julia
→ Tableaux prédictifs avec cache synchronisé
→ Statistiques différentiels complètes

2. EXPORT CSV (lignes 343-368) :
→ export_results_to_csv() : Export DataFrame pandas
→ Ajout métadonnées partie et entropie théorique

FLUX PRINCIPAL D'UTILISATION :
1. generate_entropy_report() orchestre toutes les analyses
2. Délégation Julia pour calculs lourds
3. Génération tableaux prédictifs synchronisés
4. Export CSV optionnel

POINTS CRITIQUES :
- DÉLÉGATION JULIA : Tous calculs via julia_bridge
- Cache différentiels pour synchronisation parfaite
- Rapport complet 249 lignes selon programme référence
- Mode dégradé si Julia échoue

🔧 FLUX D'EXÉCUTION PYTHON_USER_INTERFACE.PY (355 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : Menu interactif 5 options avec mode dégradé
ARCHITECTURE : Classe BaccaratUserInterface avec pont Julia

MÉTHODES CLÉS DANS LE FLUX :

1. ANALYSE PARTIE UNIQUE (lignes 44-108) :
→ analyze_single_game() : Délégation complète Julia via julia_bridge
→ Mode dégradé si Julia indisponible
→ Extraction métriques finales et positions intérêt

2. ANALYSE MULTIPLE (lignes 110-159) :
→ analyze_multiple_games() : Statistiques globales sur N parties
→ Calculs moyennes, écarts-types, min/max

3. MENU PRINCIPAL (lignes 161-334) :
→ main() : Interface interactive 5 options
→ Gestion mode non-interactif (EOFError)
→ Export automatique rapports et graphiques

FLUX PRINCIPAL D'UTILISATION :
1. main() point d'entrée avec menu interactif
2. analyze_single_game() délègue à Julia
3. Export automatique rapports et visualisations
4. Mode dégradé complet si Julia indisponible

POINTS CRITIQUES :
- DÉLÉGATION JULIA : Analyse complète via julia_bridge
- Mode dégradé robuste si Julia échoue
- Interface utilisateur complète 5 options
- Export automatique rapports et graphiques

🔧 FLUX D'EXÉCUTION PYTHON_VISUALIZATION.PY (92 lignes)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS : Double graphique entropie/diversité haute qualité
ARCHITECTURE : Classe BaccaratVisualization Python pur

MÉTHODES CLÉS DANS LE FLUX :

1. VISUALISATION PRINCIPALE (lignes 28-75) :
→ plot_entropy_evolution() : Double graphique matplotlib
→ Graphique 1 : Évolution entropie avec ligne théorique
→ Graphique 2 : Évolution diversité valeurs uniques
→ Export JPG haute qualité (300 DPI)

FLUX PRINCIPAL D'UTILISATION :
1. plot_entropy_evolution() appelée depuis interface
2. Génération double graphique haute qualité
3. Export JPG optionnel

POINTS CRITIQUES :
- PYTHON PUR : Matplotlib pour visualisation
- Double graphique entropie + diversité
- Export haute qualité 300 DPI
- Lignes théoriques pour référence
