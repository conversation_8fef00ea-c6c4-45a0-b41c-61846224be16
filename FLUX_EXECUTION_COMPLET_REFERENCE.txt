🎯 FLUX D'EXÉCUTION COMPLET - PROGRAMME RÉFÉRENCE PYTHON
═══════════════════════════════════════════════════════════

📖 ANALYSE COMPLÈTE DE : C:\Users\<USER>\Desktop\9\parallèle\entropie_baccarat_analyzer.py
📊 TOTAL : 3416 lignes - 9 CLASSES PRINCIPALES - ARCHITECTURE MODULAIRE

🔧 1. POINT D'ENTRÉE PRINCIPAL - main() (lignes 3243-3416)
═══════════════════════════════════════════════════════════

FLUX PRINCIPAL :
1. Initialisation BaccaratEntropyAnalyzer()
2. Chargement données JSON : load_baccarat_data()
3. Menu interactif 5 options
4. Exécution selon choix utilisateur

OPTION 1 - ANALYSE PARTIE UNIQUE (FLUX PRINCIPAL) :
→ analyzer.analyze_single_game(data[game_index], f"Partie_{game_index + 1}")
→ analyzer.generate_entropy_report(result)
→ Export automatique rapport{N}.txt
→ Proposition graphique et CSV

🧮 2. CLASSE PRINCIPALE - BaccaratEntropyAnalyzer (lignes 64-1147)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS CENTRALES :
- Moteur d'analyse entropique fondamental
- Calculs entropiques de base (Shannon, AEP, conditionnelle, métrique)
- Analyse de l'évolution entropique par blocs
- Génération de rapports complets

MÉTHODES CLÉS DANS LE FLUX :

2.1 __init__() (lignes 86-122) :
→ Probabilités théoriques INDEX5 EXACTES
→ Calcul entropie théorique : 3.9309 bits
→ Affichage : "🎯 Entropie théorique INDEX5: 3.9309 bits"

2.2 analyze_single_game() (lignes 550-598) :
→ extract_index5_sequence(game_data)
→ calculate_block_entropy_evolution(sequence, max_block_length=4)
→ _calculate_sequence_complexity(sequence)
→ Retourne dictionnaire complet avec entropy_evolution

2.3 calculate_block_entropy_evolution() (lignes 277-339) :
BOUCLE PRINCIPALE pour chaque position n :
→ subsequence = sequence[:n]
→ _calculate_block_entropies(subsequence, max_block_length)
→ _calculate_conditional_entropy(subsequence)
→ _estimate_metric_entropy(subsequence, max_block_length)
→ _calculate_sequence_entropy_aep(subsequence) [FORMULE AEP UNIFIÉE]

2.4 _calculate_sequence_entropy_aep() (lignes 415-453) :
FORMULE AEP CENTRALE :
H_séquence = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)
où p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)

2.5 generate_entropy_report() (lignes 771-1072) :
GÉNÉRATION RAPPORT COMPLET :
→ Initialisation INDEX5Calculator(analyzer=self)
→ Initialisation INDEX5Predictor()
→ Initialisation INDEX5PredictionValidator()
→ Initialisation INDEX5DifferentialAnalyzer()
→ Calcul différentiels : differential_analyzer.calculate_differentials(evolution)
→ BOUCLE PRÉDICTIONS : predictor.predict_next_index5(sequence_up_to_i, current_metrics)
→ BOUCLE MÉTRIQUES : calculator.calculate_all_metrics(sequence_up_to_i, current_metrics, evolution[:i+1])
→ GÉNÉRATION TABLEAUX PRÉDICTIFS

📊 3. CALCULATEUR DE MÉTRIQUES - INDEX5Calculator (lignes 1167-1717)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- 12 algorithmes de métriques avancées INDEX5
- Analyse de prédictibilité contextuelle
- Calculs de stabilité et complexité

MÉTHODE CLÉ DANS LE FLUX :

3.1 calculate_all_metrics() (lignes 1669-1716) :
CALCUL 12 MÉTRIQUES :
1. context_predictability
2. pattern_strength
3. entropy_stability
4. compression_score
5. structural_richness
6. bayesian_divergence
7. conditional_entropy_context
8. multi_algorithm_consensus
9. deterministic_pattern_score
10. bayesian_theoretical_alignment
11. transition_matrix_entropy
12. frequency_stability

🔮 4. SYSTÈME DE PRÉDICTION - INDEX5Predictor (lignes 1737-2264)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Système de prédiction multi-algorithmes
- Fusion de 3 algorithmes (déterministe, bayésien, fréquentiel)
- Respect des contraintes INDEX1 obligatoires

MÉTHODE CLÉ DANS LE FLUX :

4.1 predict_next_index5() (lignes 2049-2136) :
ALGORITHME FUSION MULTI-ALGORITHMES :
→ calculate_required_index1(current_index5) [RÈGLES DÉTERMINISTES]
→ get_valid_index5_values(required_index1) [9 VALEURS POSSIBLES]
→ predict_deterministic_patterns() [ALGORITHME 1]
→ predict_bayesian_theoretical() [ALGORITHME 2]
→ predict_frequency_based() [ALGORITHME 3]
→ FILTRAGE par contraintes INDEX1
→ VOTE PONDÉRÉ selon prédictibilité
→ Retourne prédiction finale ou "WAIT"

📈 5. ANALYSEUR DIFFÉRENTIEL - INDEX5DifferentialAnalyzer (lignes 2281-2377)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Calcul des différentiels entre mains consécutives
- Support pour les tableaux prédictifs

MÉTHODE CLÉ DANS LE FLUX :

5.1 calculate_differentials() (lignes 2303-2345) :
CALCUL DIFFÉRENTIELS :
→ diff_conditional = |current.conditional_entropy - previous.conditional_entropy|
→ diff_entropy_rate = |current.entropy_rate - previous.entropy_rate|
→ diff_simple_entropy = |current.simple_entropy - previous.simple_entropy|
→ diff_simple_entropy_theoretical = |current.simple_entropy_theoretical - previous.simple_entropy_theoretical|

📋 6. GÉNÉRATEURS DE TABLEAUX - INDEX5PredictiveDifferentialTable (lignes 2646-2996)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Génération tableaux prédictifs avec différentiels
- Application règles INDEX1 déterministes

MÉTHODE CLÉ DANS LE FLUX :

6.1 generate_predictive_table() (lignes 2966-2996) :
→ generate_predictive_table_part(sequence, evolution, analyzer, 1, 30, 1)
→ generate_predictive_table_part(sequence, evolution, analyzer, 31, 60, 2)
→ Combinaison avec légende complète

6.2 calculate_predictive_differentials() (lignes 2758-2861) :
ALGORITHME PRÉDICTIF :
→ calculate_required_index1(reference_index5) [MAIN n-1]
→ get_valid_index5_values(required_index1) [9 POSSIBILITÉS]
→ Pour chaque valeur possible :
  - Simulation séquence avec valeur ajoutée
  - Calcul métriques simulées
  - Calcul différentiels |simulé - actuel|

🎯 7. CALCULATEURS DE SCORES - INDEX5PredictiveScoreTable (lignes 2431-2627)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Calcul scores prédictifs selon formule composite
- SCORE = (DiffC + EntG) / (DiffT + DivEG)

MÉTHODE CLÉ DANS LE FLUX :

7.1 generate_predictive_score_table() (lignes 2563-2596) :
→ Utilise precomputed_differentials du cache
→ calculate_predictive_score() pour chaque cellule
→ Génération tableau avec SCORES au lieu des différentiels

✅ 8. VALIDATEUR DE PRÉDICTIONS - INDEX5PredictionValidator (lignes 3017-3221)
═══════════════════════════════════════════════════════════

RESPONSABILITÉS :
- Validation prédictions par comparaison INDEX3
- Statistiques de précision

MÉTHODE CLÉ DANS LE FLUX :

8.1 validate_prediction() (lignes 3082-3134) :
→ extract_index3(predicted_index5)
→ extract_index3(actual_index5)
→ RÈGLE TIE : Si réalité=TIE et prédiction≠TIE → NE PAS COMPTER
→ Compteurs précision globale et haute confiance

🔄 FLUX COMPLET D'EXÉCUTION POUR UNE PARTIE :
═══════════════════════════════════════════════════════════

1. main() → Choix option 1
2. analyzer.analyze_single_game()
   ├── extract_index5_sequence()
   ├── calculate_block_entropy_evolution()
   │   └── Pour chaque position : _calculate_sequence_entropy_aep()
   └── _calculate_sequence_complexity()
3. analyzer.generate_entropy_report()
   ├── Initialisation 4 classes : Calculator, Predictor, Validator, DifferentialAnalyzer
   ├── differential_analyzer.calculate_differentials()
   ├── BOUCLE PRÉDICTIONS :
   │   ├── calculator.calculate_all_metrics()
   │   └── predictor.predict_next_index5()
   ├── BOUCLE VALIDATION :
   │   └── validator.validate_prediction()
   ├── GÉNÉRATION TABLEAUX :
   │   ├── INDEX5PredictiveDifferentialTable.generate_predictive_table()
   │   └── INDEX5PredictiveScoreTable.generate_predictive_score_table()
   └── validator.get_detailed_report()
4. Export automatique rapport{N}.txt
5. Proposition graphique et CSV

🎯 RÉSULTAT FINAL : RAPPORT COMPLET AVEC TOUTES LES ANALYSES
═══════════════════════════════════════════════════════════
